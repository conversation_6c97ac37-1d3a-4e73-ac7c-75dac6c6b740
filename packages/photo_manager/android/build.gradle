import java.util.regex.Matcher
import java.util.regex.Pattern

group 'com.fluttercandies.photo_manager'
version '1.0-SNAPSHOT'

buildscript {
    ext.kotlin_version = '1.8.10'
    repositories {
        google()
        mavenCentral()
    }

    dependencies {
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
    }
}

rootProject.allprojects {
    repositories {
        google()
        mavenCentral()
    }
}

apply plugin: 'com.android.library'
apply plugin: 'kotlin-android'

void log(Object msg) {
    logger.log(LogLevel.INFO, "[PhotoManager] $msg")
}

/**
 * Get java version from JAVA_HOME
 * @param path JAVA_HOME path
 * @return java version
 */
JavaVersion getJavaFromHome(String path) {
    def releaseFile = new File(path, "release")

    if (releaseFile.exists()) {
        def pattern = Pattern.compile("JAVA_VERSION=\"(\\d+\\.\\d+)\"")
        def matcher = pattern.matcher(releaseFile.text)
        if (matcher.find()) {
            def version = matcher.group(1)
            log("Get java version from release file: $version")
            return JavaVersion.toVersion(version)
        }
    } else {
        def javaBin = new File("${path}/bin/java")
        if (javaBin.exists()) {
            return JavaVersion.VERSION_1_8
        } else if (new File("${path}/bin/java.exe").exists()) {
            return JavaVersion.VERSION_1_8
        }

        return null
    }
}

JavaVersion getJavaVersion() {
    try {
        JavaVersion res

        def javaVersion = project.rootProject.findProperty("java.version")
        if (javaVersion != null) {
            res = JavaVersion.toVersion(javaVersion)
            log("Get java version from project's gradle.properties: $javaVersion")
            if (res != null) {
                return res
            }
        }

        String javaHome
        javaHome = System.getenv("JAVA_HOME")
        if (res == null && javaHome != null) {
            res = getJavaFromHome(javaHome)
            log("Get java version from JAVA_HOME: $javaHome")
            if (res != null) {
                return res
            }
        }

        javaHome = project.rootProject.findProperty("java.home")
        if (res == null && javaHome != null) {
            res = getJavaFromHome(javaHome)
            log("Get java version from gradle.properties: $javaHome")
            if (res != null) {
                return res
            }
        }

        javaHome = System.getProperty("java.home")
        if (res == null && javaHome != null) {
            res = getJavaFromHome(javaHome)
            log("Get java version from java.home: $javaHome")
            if (res != null) {
                return res
            }
        }

        if (res != null) {
            return res
        }
    } catch (Exception e) {
        e.printStackTrace()
    }

    return JavaVersion.VERSION_1_8
}

android {
    if (project.android.hasProperty('namespace') ||
            getGradle().getGradleVersion().substring(0, 1).toInteger() >= 8) {
        namespace 'com.flutterandies.photo_manager'
    }

    compileSdkVersion 34

    sourceSets {
        main.java.srcDirs += 'src/main/kotlin'
    }

    compileOptions {
//        def version = getJavaVersion()
//        sourceCompatibility = version
//        targetCompatibility = version
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
    kotlinOptions {
        jvmTarget = '1.8'
    }
    defaultConfig {
        minSdkVersion 23
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }
}

dependencies {
    implementation 'com.github.bumptech.glide:glide:4.15.1'
    implementation 'androidx.exifinterface:exifinterface:1.3.7'
}