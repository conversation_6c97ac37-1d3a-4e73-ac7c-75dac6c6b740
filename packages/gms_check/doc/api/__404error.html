<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, height=device-height, initial-scale=1, user-scalable=no">
  <meta name="generator" content="made with love by dartdoc 7.0.0-dev">
  <meta name="description" content="gms_check API docs, for the Dart programming language.">
  <title>gms_check - Dart API docs</title>


  
  <link rel="preconnect" href="https://fonts.gstatic.com">
  <link href="https://fonts.googleapis.com/css2?family=Roboto+Mono:ital,wght@0,300;0,400;0,500;0,700;1,400&display=swap" rel="stylesheet">
  <link href="https://fonts.googleapis.com/css2?family=Material+Symbols+Outlined:opsz,wght,FILL,GRAD@48,400,0,0" rel="stylesheet">
  
  <link rel="stylesheet" href="static-assets/github.css?v1">
  <link rel="stylesheet" href="static-assets/styles.css?v1">
  <link rel="icon" href="static-assets/favicon.png?v1">

  
</head>


<body data-base-href="" data-using-base-href="false" class="light-theme">

<div id="overlay-under-drawer"></div>

<header id="title">
  <span id="sidenav-left-toggle" class="material-symbols-outlined" role="button" tabindex="0">menu</span>
  <ol class="breadcrumbs gt-separated dark hidden-xs">
    <li><a href="https://github.com/simonpham/gms_check">gms_check package</a></li>
  </ol>
  <div class="self-name">gms_check</div>
  <form class="search navbar-right" role="search">
    <input type="text" id="search-box" autocomplete="off" disabled class="form-control typeahead" placeholder="Loading search...">
  </form>
  <div class="toggle" id="theme-button">
    <label for="theme">
      <input type="checkbox" id="theme" value="light-theme">
      <span id="dark-theme-button" class="material-symbols-outlined">
        brightness_4
      </span>
      <span id="light-theme-button" class="material-symbols-outlined">
        brightness_5
      </span>
    </label>
  </div>
</header>
<main>

  <div id="dartdoc-main-content" class="main-content">
    <h1>404: Something's gone wrong :-(</h1>

    <section class="desc">
      <p>You've tried to visit a page that doesn't exist.  Luckily this site
         has other <a href="index.html">pages</a>.</p>
      <p>If you were looking for something specific, try searching:
      <form class="search-body" role="search">
        <input type="text" id="search-body" autocomplete="off" disabled class="form-control typeahead" placeholder="Loading search...">
      </form>
      </p>

    </section>
  </div> <!-- /.main-content -->

  <div id="dartdoc-sidebar-left" class="sidebar sidebar-offcanvas-left">
    <!-- The search input and breadcrumbs below are only responsively visible at low resolutions. -->
<header id="header-search-sidebar" class="hidden-l">
  <form class="search-sidebar" role="search">
    <input type="text" id="search-sidebar" autocomplete="off" disabled class="form-control typeahead" placeholder="Loading search...">
  </form>
</header>

<ol class="breadcrumbs gt-separated dark hidden-l" id="sidebar-nav">
    <li><a href="https://github.com/simonpham/gms_check">gms_check package</a></li>
</ol>


    <h5><span class="package-name">gms_check</span> <span class="package-kind">package</span></h5>
    <ol>
      <li class="section-title">Libraries</li>
      <li><a href="gms_check/gms_check-library.html">gms_check</a></li>
</ol>

  </div>

  <div id="dartdoc-sidebar-right" class="sidebar sidebar-offcanvas-right">
  </div>

</main>

<footer>
  <span class="no-break">
    gms_check
      1.0.2
  </span>

  
</footer>



<script src="static-assets/highlight.pack.js?v1"></script>
<script src="static-assets/docs.dart.js"></script>



</body>

</html>

