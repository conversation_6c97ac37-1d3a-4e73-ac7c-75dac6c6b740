.light-theme {
  /*background-color body, listdropdown*/
  --main-bg-color: #fff;
  /*header id-tittle*/
  --main-header-color: #eeeeee;
  /*package-name*/
  --main-sidebar-color: #727272;
  /*section-title and section subtitle, desc markdown(body, dd, h3), header a*/
  --main-text-color: #111111;
  /*typehead search-box*/
  --main-search-bar: #fff;
  /* scrollbar-thumb */
  --main-scrollbar-color: #CCC;
  /* footer */
  --main-footer-background: #111111;
  /*header text color*/
  --main-h-text: black;
  /* hyperlinks*/
  --main-hyperlinks-color: #0175C2;
  /*search background*/
  --main-search-background: transparent;

  /*code snippets*/
  --main-code-bg: #f8f8f8;
  --main-keyword-color: #333;
  --main-tag-color: #000080;
  --main-section-color: #900;
  --main-comment-color: #998;
  --main-var-color: #008080;
  --main-string-color: #d14;

  --main-number-filter: invert(0%);
  --main-icon-color: black;
}

.dark-theme {
  /*background-color body, listdropdown*/
  --main-bg-color: #10161E;
  /*header id-tittle*/
  --main-header-color: #1C2834;
  /*package-name*/
  --main-sidebar-color: #fff;
  /*section-title and section subtitle, desc markdown(body, dd, h3), header a*/
  --main-text-color: #fff;
  /*typehead search-box*/
  --main-search-bar: #454545;
  /* scrollbar-thumb */
  --main-scrollbar-color: #5f6368;
  /* footer */
  --main-footer-background: #27323a;
  /* hyperlinks*/
  --main-hyperlinks-color: #00D2FA;
  /*search background*/
  --main-search-background: black;

  /*code snippets*/
  --main-code-bg: #10161E;
  --main-keyword-color: white;
  --main-tag-color: #00D2FA;
  --main-section-color: #FF2D64;
  --main-comment-color: #909CC3;
  --main-var-color: #55A09B;
  --main-string-color: #FF2D64;

  --main-number-filter: invert(100%);
  --main-icon-color: white;
}

#theme {
  display: none;
}

#theme-button {
  position: absolute;
  right: 30px;
  height: 24px;
}

#theme-button .material-symbols-outlined {
  color: var(--main-icon-color);
  user-select: none;
  cursor: pointer;
}

.light-theme #light-theme-button {
  display: none;
}

.dark-theme #dark-theme-button {
  display: none;
}

/*
Only show images that fit their theme using GitHub's syntax, see:
https://github.blog/changelog/2021-11-24-specify-theme-context-for-images-in-markdown/
*/
.dark-theme img[src$="#gh-light-mode-only"] {
  display: none;
}

.light-theme img[src$="#gh-dark-mode-only"] {
  display: none;
}

/* for layout */
html,
body {
  margin: 0;
  padding: 0;
  height: 100%;
  width: 100%;
  overflow: hidden;
  box-sizing: border-box;
}

*, *:before, *:after {
  box-sizing: inherit;
}

body {
  display: flex;
  flex-direction: column;
  -webkit-overflow-scrolling: touch;
}

header {
  flex: 0 0 50px;
  display: flex;
  flex-direction: row;
  align-items: center;
  padding-left: 30px;
  padding-right: 30px;
  background-color: var(--main-header-color);
}

header ol {
  list-style: none;
  margin: 0;
  padding: 0;
}

header ol li {
  display: inline;
}

header form {
  display: flex;
  flex: 1;
  justify-content: flex-end;
}

header#header-search-sidebar {
  height: 50px;
  margin-bottom: 25px;
}

footer {
  flex: 0 0 16px;
  text-align: center;
  padding: 16px 20px;
}

main {
  flex: 1;
  display: flex;
  flex-direction: row;
  padding: 20px;
  min-height: 0;
}

.sidebar-offcanvas-left {
  flex: 0 1 230px;
  order: 1;
  overflow-y: scroll;
  padding: 20px 0 15px 30px;
  margin: 5px 20px 0 0;
}

::-webkit-scrollbar-button{ display: none; height: 13px; border-radius: 0; background-color: #AAA; }
::-webkit-scrollbar-button:hover{ background-color: #AAA; }
::-webkit-scrollbar-thumb{ background-color: var(--main-scrollbar-color); }
::-webkit-scrollbar-thumb:hover{ background-color: var(--main-scrollbar-color); }
::-webkit-scrollbar{ width: 4px; }

.main-content::-webkit-scrollbar{ width: 8px; }

.main-content {
  flex: 1;
  order: 2;
  overflow-y: scroll;
  padding: 10px 20px 0 20px;
}

.sidebar-offcanvas-right {
  flex: 0 1 12em;
  order: 3;
  overflow-y: scroll;
  padding: 20px 15px 15px 15px;
  margin-top: 5px;
  margin-right: 20px;
}
/* end for layout */

body {
  -webkit-text-size-adjust: 100%;
  overflow-x: hidden;
  font-family: Roboto, sans-serif;
  font-size: 16px;
  line-height: 1.42857143;
  color: var(--main-text-color);
  background-color: var(--main-bg-color);
}

nav.navbar {
  background-color: inherit;
  min-height: 50px;
  border: 0;
}

@media (max-width: 840px) {
  .hidden-xs {
    display: none !important;
  }
}

@media (min-width: 841px) {
  .hidden-l {
    display: none !important;
  }
}

nav.navbar .row {
  padding-top: 8px;
}

nav .container {
  white-space: nowrap;
}

header {
  background-color: var(--main-header-color);
  box-shadow: 0 3px 5px rgba(0,0,0,0.1);
}

.pre {
    border: 1px solid #ddd;
    font-size: 14px;
}

.hljs-string, .hljs-doctag {
    color: var(--main-string-color);
}

.hljs-number, .hljs-literal, .hljs-variable, .hljs-template-variable, .hljs-tag .hljs-attr {
    color: var(--main-var-color);
}

.hljs-comment, .hljs-quote {
    color: var(--main-comment-color);
    font-style: italic;
}

.hljs-title, .hljs-section, .hljs-selector-id {
    color: var(--main-section-color);
    font-weight: bold;
}

.hljs-tag, .hljs-name, .hljs-attribute {
    color: var(--main-tag-color);
    font-weight: normal;
}

.hljs-keyword, .hljs-selector-tag, .hljs-subst {
    color: var(--main-keyword-color);
    font-weight: bold;
}

.hljs {
    display: block;
    overflow-x: auto;
    padding: 0.5em;
    color: var(--main-text-color);
    background: var(--main-code-bg);
}

a {
  text-decoration: none;
}

section {
  margin-bottom: 36px;
}

dl {
  margin: 0;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  font-family: Roboto, sans-serif;
  font-weight: 400;
  margin-top: 1.5em;
  color: var(--main-text-color);
}

h1.title {
  overflow: hidden;
  text-overflow: ellipsis;
}

h1 {
  font-size: 37px;
  margin-top: 0;
  margin-bottom: 0.67em;
}

h2 {
  font-size: 28px;
}

h5 {
  font-size: 16px;
}

p {
  margin-bottom: 1em;
  margin-top: 0;
}

a {
  color: var(--main-hyperlinks-color);
}

a:hover {
  color: #13B9FD;
}

pre.prettyprint {
  font-family: 'Roboto Mono', Menlo, monospace;
  color: black;
  border-radius: 0;
  font-size: 15px;
  word-wrap: normal;
  line-height: 1.4;
  border: 0;
  margin: 16px 0 16px 0;
  padding: 8px;
}

pre code {
  white-space: pre;
  word-wrap: initial;
  font-size: 100%
}

.fixed {
  white-space: pre;
}

pre {
  border: 1px solid #ddd;
  background-color: #eee;
  font-size: 14px;
}

code {
  font-family: 'Roboto Mono', Menlo, monospace;
  color: inherit;
  padding: 0.2em 0.4em;
  font-size: 85%;
  background-color: rgba(27,31,35,0.05);
  border-radius: 3px;
}

@media(max-width: 840px) {
  nav .container {
    width: 100%
  }

  h1 {
    font-size: 24px;
  }

  pre {
    margin: 16px 0;
  }
}

header h1 {
  font-weight: 400;
  margin-bottom: 16px;
}

header a,
header p,
header li {
  color: #0175C2;
}

header a:hover {
  color: #0175C2;
}

header h1 .kind {
  color: #555;
}

dt {
  font-weight: normal;
}

dd {
  color: var(--main-text-color);
  margin-bottom: 1em;
  margin-left: 0;
}

dd.callable, dd.constant, dd.property {
  margin-bottom: 24px;
}

dd p {
  overflow-x: hidden;
  text-overflow: ellipsis;
  margin-bottom: 0;
}

/* Enum values do not have their own pages; their full docs are presented on the
 * enum class's page. */
dt.constant + dd p {
  margin-bottom: 1em;
}

/* indents wrapped lines */
section.summary dt {
  margin-left: 24px;
  text-indent: -24px;
}

.dl-horizontal dd {
  margin-left: initial;
}

dl.dl-horizontal dt {
  font-style: normal;
  text-align: left;
  color: #727272;
  margin-right: 20px;
  width: initial;
}

dt .name {
  font-weight: 500;
}

dl dt.callable .name {
  float: none;
  width: auto;
}

.type-parameter {
  white-space: nowrap;
}

.multi-line-signature .type-parameter .parameter {
  margin-left: 0;
  display: unset;
}

.parameter-list {
  display: table-cell;
  margin-left: 10px;
  list-style-type: none;
  padding-inline-start: unset;
}

.signature {
  color: var(--main-text-color);
}

.signature a {
  color: var(--main-hyperlinks-color);
}

.optional {
  font-style: italic;
}

.undocumented {
  font-style: italic;
}

.is-const {
  font-style: italic;
}

.deprecated {
  text-decoration: line-through;
}

.category.linked {
  font-weight: bold;
  opacity: 1;
}

/* Colors for category based on categoryOrder in dartdoc_options.config. */
.category.cp-0 {
  background-color: #54b7c4
}

.category.cp-1 {
  background-color: #54c47f
}

.category.cp-2 {
  background-color: #c4c254
}

.category.cp-3 {
  background-color: #c49f54
}

.category.cp-4 {
  background-color: #c45465
}

.category.cp-5 {
  background-color: #c454c4
}

.category a {
  color: white;
}

.category {
  padding: 2px 4px;
  font-size: 12px;
  border-radius: 4px;
  background-color: #999;
  text-transform: uppercase;
  color: white;
  opacity: .5;
}

h1 .category {
  vertical-align: middle;
}

/* The badge under a declaration for things like "const", "read-only", etc. and for the badges inline like sealed or interface */
/* See https://github.com/dart-lang/dartdoc/blob/main/lib/src/model/feature.dart */
.feature {
  display: inline-block;
  background: var(--main-bg-color);
  border: 1px solid var(--main-hyperlinks-color);
  border-radius: 20px;
  color: var(--main-hyperlinks-color);

  font-size: 12px;
  padding: 1px 6px;
  margin: 0 8px 0 0;
}

a.feature:hover {
  border-color: #13B9FD;
}

h1 .feature {
  vertical-align: middle;
  margin: 0 -2px 0 0;
}

.source-link {
  padding: 18px 4px;
  font-size: 18px;
  vertical-align: middle;
}

@media (max-width: 840px) {
  .source-link {
    padding: 7px 2px;
    font-size: 10px;
  }
}

#external-links {
  float: right;
}

.btn-group {
  position: relative;
  display: inline-flex;
  vertical-align: middle;
}

footer {
  color: #fff;
  background-color: var(--main-footer-background);
  width: 100%;
}

footer p {
  margin: 0;
}

footer .no-break {
  white-space: nowrap;
}

footer .container {
  padding-left: 0;
  padding-right: 0;
}

footer a, footer a:hover {
  color: #fff;
}

.markdown.desc {
  max-width: 700px;
}

.markdown h1 {
  font-size: 24px;
  margin-bottom: 8px;
}

.markdown h2 {
  font-size: 20px;
  margin-top: 24px;
  margin-bottom: 8px;
}

.markdown h3 {
  font-size: 18px;
  margin-bottom: 8px;
  color: var(--main-text-color);
}

.markdown h4 {
  font-size: 16px;
  margin-bottom: 0;
}

.markdown li p {
  margin: 0;
}

table {
  margin-bottom: 1em;
}

table,
th,
td {
  border: 1px solid lightgrey;
  border-collapse: collapse;
}

th,
td {
  padding: 8px;
}

.gt-separated {
  list-style: none;
  padding: 0;
  margin: 0;
}

.gt-separated li {
  display: inline-block;
}

.gt-separated li:before {
  background-image: url("data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 16 16'><path fill='%23DDDDDD' d='M6.7,4L5.7,4.9L8.8,8l-3.1,3.1L6.7,12l4-4L6.7,4z'/></svg>");
  background-position: center;
  content: "\00a0";
  margin: 0 6px 0 4px;
  padding: 0 3px 0 0;
}

.gt-separated.dark li:before {
  background-image: url("data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 16 16'><path fill='%23727272' d='M6.7,4L5.7,4.9L8.8,8l-3.1,3.1L6.7,12l4-4L6.7,4z'/></svg>");
}

.gt-separated li:first-child:before {
  background-image: none;
  content: "";
  margin: 0;
  padding: 0;
}

.multi-line-signature {
  font-size: 17px;
  color: #727272;
}

.multi-line-signature .parameter {
  margin-left: 24px;
  display: block;
}

.breadcrumbs {
  padding: 0;
  margin: 8px 0 8px 0;
  white-space: nowrap;
  line-height: 1;
}

@media screen and (min-width: 840px) {
  nav ol.breadcrumbs {
    float: left;
  }
}

@media screen and (max-width: 840px) {
  .breadcrumbs {
    margin: 0 0 24px 0;
    overflow-x: hidden;
  }
}

.breadcrumbs .gt-separated .dark .hidden-xs li+li:before {
  color: var(--main-h-text);
}

ol.breadcrumbs li a {
  color: var(--main-hyperlinks-color);
}

.self-crumb {
  color: var(--main-h-text);
}

.self-name {
  color: #555;
  display: none;
}

.annotation-list {
  list-style: none;
  padding: 0;
  display: inline;
}

.comma-separated {
  list-style: none;
  padding: 0;
  display: inline;
}

.comma-separated li {
  display: inline;
}

.comma-separated li:after {
  content: ", ";
}

.comma-separated li:last-child:after {
  content: "";
}

.end-with-period li:last-child:after {
  content: ".";
}

.container > section:first-child {
  border: 0;
}

.constructor-modifier {
  font-style: italic;
}

section.multi-line-signature div.parameters {
  margin-left: 24px;
}

/* sidebar styles */

.sidebar ol {
  list-style: none;
  line-height: 22px;
  margin-top: 0;
  margin-bottom: 0;
  padding: 0 0 15px 0;
}

.sidebar h5 a,
.sidebar h5 a:hover {
  color: var(--main-sidebar-color);
}

.sidebar h5,
.sidebar ol li {
  text-overflow: ellipsis;
  overflow: hidden;
  padding: 3px 0 3px 3px;
}

.sidebar h5 {
  color: var(--main-sidebar-color);
  font-size: 18px;
  margin: 0 0 22px 0;
  padding-top: 0;
}

.sidebar ol li.section-title {
  font-size: 18px;
  font-weight: normal;
  text-transform: uppercase;
  padding-top: 25px;
}

.sidebar ol li.section-subtitle a {
  color: inherit;
}

.sidebar ol li.section-subtitle {
  font-weight: 400;
  text-transform: uppercase;
}

.sidebar ol li.section-subitem {
  margin-left: 12px;
}

.sidebar ol li:first-child {
  padding-top: 3px;
  margin-top: 0;
}

button {
  padding: 0;
}

#sidenav-left-toggle {
  display: none;
  vertical-align: text-bottom;
  padding: 0;
  color: var(--main-icon-color);
  user-select: none;
  cursor: pointer;
}

/* left-nav disappears, and can transition in from the left */
@media screen and (max-width:840px) {
  #sidenav-left-toggle {
    display: inline;
    width: 24px;
    height: 24px;
    border: none;
    margin-right: 24px;
    margin-left: 24px;
    font-size: 24px;
  }

  #overlay-under-drawer.active {
    opacity: 0.4;
    height: 100%;
    z-index: 1999;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: black;
    display: block;
  }

  .sidebar-offcanvas-left {
    left: -100%;
    position: fixed;
    -webkit-transition:all .25s ease-out;
    -o-transition:all .25s ease-out;
    transition:all .25s ease-out;
    z-index: 2000;
    top: 0;
    width: 280px; /* works all the way down to an iphone 4 */
    height: 90%;
    background-color: var(--main-bg-color);
    overflow-y: scroll; /* TODO: how to hide scroll bars? */
    padding: 10px;
    margin: 10px 10px;
    box-shadow: 5px 5px 5px 5px #444444;
  }

  ol#sidebar-nav {
    font-size: 18px;
    white-space: pre-line;
  }

  .sidebar-offcanvas-left.active {
    left: 0; /* this animates our drawer into the page */
  }

  .self-name {
    display: inline-block;
    color: var(--main-hyperlinks-color);
  }
}

.sidebar-offcanvas-left h5 {
  margin-bottom: 10px;
}

.sidebar-offcanvas-left h5:last-of-type {
  border: 0;
  margin-bottom: 25px;
}

/* the right nav disappears out of view when the window shrinks */
@media screen and (max-width: 992px) {
  .sidebar-offcanvas-right {
     display: none;
   }
}

#overlay-under-drawer {
  display: none;
}

/* find-as-you-type search box */

.form-control {
  border-radius: 0;
  border: 0;
}

@media screen and (max-width: 840px) {
  form.search {
    display: none;
  }
}

.typeahead {
  width: 200px;
  padding: 2px 7px 1px 7px;
  line-height: 20px;
  outline: none;
}

.tt-wrapper {
  position: relative;
  display: inline-block;
}

.tt-input {
  position: relative;
  vertical-align: top;
}

.navbar-right .tt-menu {
  right: 0;
  left: inherit !important;
  width: 540px;
  max-height: 280px;
  overflow-y: scroll;
}

.navbar-right {
  padding-right: 60px;
}

.tt-menu {
  position: absolute;
  top: 100%;
  left: 0;
  z-index: 100;
  font-size: 14px;
  margin: 0;
  background-color: var(--main-bg-color);
  border: 1px solid var(--main-header-color);
  -webkit-box-shadow: 0 5px 10px rgba(0,0,0,.2);
     -moz-box-shadow: 0 5px 10px rgba(0,0,0,.2);
          box-shadow: 0 5px 10px rgba(0,0,0,.2);
}


.typeahead {
  padding: 17px 17px 17px 50px;
  width: 422px;
  height: 20px;
  font-size: 13px;
  background-image: url("./search.svg");
  background-repeat: no-repeat;
  background-position: 4%;
  outline: 0;
  background-size: 20px;
  filter: var(--main-number-filter);
    -webkit-filter: var(--main-number-filter);
}

.search-summary {
  margin-bottom: 10px;
}

a.tt-container {
  font-size: 16px;
  color: var(--main-hyperlinks-color);
}

.enter-search-message {
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  background-color: #AAA;
  padding: 0;
  font-size: 14px;
  margin: 0;
  clear: both;
  text-align: center;
  color: black;
}

.tt-suggestion:hover {
  cursor: pointer;
  color: #fff;
  background-color: #0097cf;
}

.tt-suggestion:hover .search-from-lib {
  color: #ddd;
}

.tt-suggestion.tt-cursor {
  color: #fff;
  background-color: #0097cf;
}

.tt-suggestion.tt-cursor .search-from-lib {
  color: #ddd;
}

.tt-suggestion p {
  margin: 0;
}

.tt-container {
  font-size: 14px;
  margin-bottom: 0;
  margin-top: 15px;
}

.tt-container-text {
  color: var(--main-text-color);
}


/* Search results formatting for mini results below search bar. */

.tt-search-results .tt-container {
  margin-top: 5px;
  margin-bottom: 5px;
}

/* Do not show the container as a section. */
.tt-search-results .tt-container-text {
  display: none
}

/* An inline style. */
.tt-search-results .tt-suggestion {
  color: var(--main-text-color);
  margin-top: 5px;
  overflow: hidden;
  padding-left: 10px;
  padding-right: 10px;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.tt-search-results .tt-suggestion-title {
  font-size: 14px;
  padding-right: 5px;
}

.tt-search-results .tt-suggestion-container {
  color: var(--main-keyword-color);
  font-size: 14px;
  font-style: italic;
  padding-right: 5px;
}

.tt-search-results .one-line-description {
  color: var(--main-keyword-color);
  display: inline;
  margin-left: 0;
}


.tt-search-results .one-line-description::before {
  content: open-quote;
}

.tt-search-results .one-line-description::after {
  content: close-quote;
}

/* Search results formatting for `search.html`. */

/* A block style. */
#dartdoc-main-content .tt-suggestion {
  color: var(--main-text-color);
  margin-top: 5px;
  margin-bottom: 10px;
  border-style: solid;
  border-color: lightgrey;
  border-width: 0.5px;
}

#dartdoc-main-content .tt-suggestion-title {
  display: block;
  font-weight: 500;
  margin: 4px 10px 0;
}

#dartdoc-main-content .one-line-description {
  display: block;
  margin: 2px 10px 3px;
}

/* Do not show a result's container. */
#dartdoc-main-content .tt-suggestion-container {
  display: none;
}

@media screen and (max-width: 840px) {
 .typeahead {
   padding: 17px 17px 17px 33px;
   width: 240px;
   height: 17px;
   border: 1px solid #f5f5f5;
   background-position: 3%;
   margin: 10px 10px 10px 9px;
 }

  header {
    padding-left: 0;
  }
}

@media screen and (max-width: 320px) {
  #sidenav-left-toggle {
    margin-right: 10px;
    margin-left: 20px;
  }

  .self-name {
    margin-right: 10px;
  }
}

::placeholder {
  filter: brightness(0.85);
}

.search-body {
  border: 1px solid #7f7f7f;
  max-width: 400px;
  box-shadow: 3px 3px 5px rgba(0,0,0,0.1);
}

section#setter {
  border-top: 1px solid #ddd;
  padding-top: 36px;
}

li.inherited a {
  opacity: 0.65;
  font-style: italic;
}

#instance-methods dt.inherited .name,
#instance-properties dt.inherited .name,
#operators dt.inherited .name {
  font-weight: 400;
  font-style: italic;
}

#instance-methods dt.inherited .signature,
#instance-properties dt.inherited .signature,
#operators dt.inherited .signature {
  font-weight: 400;
}

@media print {
  .subnav, .sidebar {
    display: none;
  }

  a[href]:after {
   content: "" !important;
  }
}