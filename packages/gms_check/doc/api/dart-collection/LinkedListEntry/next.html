<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, height=device-height, initial-scale=1, user-scalable=no">
  <meta name="description" content="API docs for the next property from the LinkedListEntry class, for the Dart programming language.">
  <title>next property - LinkedListEntry class - dart:collection library - Dart API</title>
  <!-- required because all the links are pseudo-absolute -->
  <base href="../..">

  <link href="https://fonts.googleapis.com/css?family=Source+Code+Pro:500,400i,400,300|Source+Sans+Pro:400,300,700" rel="stylesheet">
  <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
  <link rel="stylesheet" href="static-assets/github.css">
  <link rel="stylesheet" href="static-assets/styles.css">
  <link rel="icon" href="static-assets/favicon.png">
  <!-- header placeholder -->
</head>

<body>

<div id="overlay-under-drawer"></div>

<header id="title">
  <button id="sidenav-left-toggle" type="button">&nbsp;</button>
  <ol class="breadcrumbs gt-separated dark hidden-xs">
    <li><a href="index.html">gms_check</a></li>
    <li><a href="dart-collection/dart-collection-library.html">dart:collection</a></li>
    <li><a href="dart-collection/LinkedListEntry-class.html">LinkedListEntry<span class="signature">&lt;<wbr><span class="type-parameter">E extends LinkedListEntry&lt;<wbr><span class="type-parameter">E</span>&gt;</span>&gt;</span></a></li>
    <li class="self-crumb">next property</li>
  </ol>
  <div class="self-name">next</div>
  <form class="search navbar-right" role="search">
    <input type="text" id="search-box" autocomplete="off" disabled class="form-control typeahead" placeholder="Loading search...">
  </form>
</header>

<main>

  <div id="dartdoc-sidebar-left" class="col-xs-6 col-sm-3 col-md-2 sidebar sidebar-offcanvas-left">
    <header id="header-search-sidebar" class="hidden-l">
      <form class="search-sidebar" role="search">
        <input type="text" id="search-sidebar" autocomplete="off" disabled class="form-control typeahead" placeholder="Loading search...">
      </form>
    </header>
    
    <ol class="breadcrumbs gt-separated dark hidden-l" id="sidebar-nav">
      <li><a href="index.html">gms_check</a></li>
      <li><a href="dart-collection/dart-collection-library.html">dart:collection</a></li>
      <li><a href="dart-collection/LinkedListEntry-class.html">LinkedListEntry<span class="signature">&lt;<wbr><span class="type-parameter">E extends LinkedListEntry&lt;<wbr><span class="type-parameter">E</span>&gt;</span>&gt;</span></a></li>
      <li class="self-crumb">next property</li>
    </ol>
    
    <h5>LinkedListEntry class</h5>
    <ol>
    
        <li class="section-title"><a href="dart-collection/LinkedListEntry-class.html#constructors">Constructors</a></li>
        <li><a href="dart-collection/LinkedListEntry/LinkedListEntry.html">LinkedListEntry</a></li>
    
        <li class="section-title">
            <a href="dart-collection/LinkedListEntry-class.html#instance-properties">Properties</a>
        </li>
        <li><a href="dart-collection/LinkedListEntry/list.html">list</a></li>
        <li><a href="dart-collection/LinkedListEntry/next.html">next</a></li>
        <li><a href="dart-collection/LinkedListEntry/previous.html">previous</a></li>
        <li class="inherited"><a href="dart-core/Object/hashCode.html">hashCode</a></li>
        <li class="inherited"><a href="dart-core/Object/runtimeType.html">runtimeType</a></li>
    
        <li class="section-title"><a href="dart-collection/LinkedListEntry-class.html#instance-methods">Methods</a></li>
        <li><a href="dart-collection/LinkedListEntry/insertAfter.html">insertAfter</a></li>
        <li><a href="dart-collection/LinkedListEntry/insertBefore.html">insertBefore</a></li>
        <li><a href="dart-collection/LinkedListEntry/unlink.html">unlink</a></li>
        <li class="inherited"><a href="dart-core/Object/noSuchMethod.html">noSuchMethod</a></li>
        <li class="inherited"><a href="dart-core/Object/toString.html">toString</a></li>
    
        <li class="section-title inherited"><a href="dart-collection/LinkedListEntry-class.html#operators">Operators</a></li>
        <li class="inherited"><a href="dart-core/Object/operator_equals.html">operator ==</a></li>
    
    
    
    
    
    </ol>
  </div><!--/.sidebar-offcanvas-->

  <div id="dartdoc-main-content" class="col-xs-12 col-sm-9 col-md-8 main-content">
      <div><h1><span class="kind-property">next</span> property</h1></div>


        <section id="getter">
        
        <section class="multi-line-signature">
          <span class="returntype">E</span>
                  <span class="name ">next</span>
          
</section>
        
                <section class="desc markdown">
          <p>The successor of this element in its linked list.</p>
<p>The value is  <code>null</code> if there is no successor in the linked list,
or if this entry is not currently in any list.</p>
        </section>
                <section class="summary source-code" id="source">
          <h2><span>Implementation</span></h2>
          <pre class="language-dart"><code class="language-dart">E? get next {
  if (_list == null || identical(_list!.first, _next)) return null;
  return _next;
}</code></pre>
        </section>
</section>
        
  </div> <!-- /.main-content -->

  <div id="dartdoc-sidebar-right" class="col-xs-6 col-sm-6 col-md-2 sidebar sidebar-offcanvas-right">
  </div><!--/.sidebar-offcanvas-->

</main>

<footer>
  <span class="no-break">
    gms_check
      1.0.0
  </span>

  <!-- footer-text placeholder -->
</footer>

<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.2.1/jquery.min.js"></script>
<script src="static-assets/typeahead.bundle.min.js"></script>
<script src="static-assets/highlight.pack.js"></script>
<script src="static-assets/URI.js"></script>
<script src="static-assets/script.js"></script>
<!-- footer placeholder -->

</body>

</html>
