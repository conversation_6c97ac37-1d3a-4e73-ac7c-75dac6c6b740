<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, height=device-height, initial-scale=1, user-scalable=no">
  <meta name="description" content="API docs for the removeWhere method from the UnmodifiableListView class, for the Dart programming language.">
  <title>removeWhere method - UnmodifiableListView class - dart:collection library - Dart API</title>
  <!-- required because all the links are pseudo-absolute -->
  <base href="../..">

  <link href="https://fonts.googleapis.com/css?family=Source+Code+Pro:500,400i,400,300|Source+Sans+Pro:400,300,700" rel="stylesheet">
  <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
  <link rel="stylesheet" href="static-assets/github.css">
  <link rel="stylesheet" href="static-assets/styles.css">
  <link rel="icon" href="static-assets/favicon.png">
  <!-- header placeholder -->
</head>

<body>

<div id="overlay-under-drawer"></div>

<header id="title">
  <button id="sidenav-left-toggle" type="button">&nbsp;</button>
  <ol class="breadcrumbs gt-separated dark hidden-xs">
    <li><a href="index.html">gms_check</a></li>
    <li><a href="dart-collection/dart-collection-library.html">dart:collection</a></li>
    <li><a href="dart-collection/UnmodifiableListView-class.html">UnmodifiableListView<span class="signature">&lt;<wbr><span class="type-parameter">E</span>&gt;</span></a></li>
    <li class="self-crumb">removeWhere method</li>
  </ol>
  <div class="self-name">removeWhere</div>
  <form class="search navbar-right" role="search">
    <input type="text" id="search-box" autocomplete="off" disabled class="form-control typeahead" placeholder="Loading search...">
  </form>
</header>

<main>

  <div id="dartdoc-sidebar-left" class="col-xs-6 col-sm-3 col-md-2 sidebar sidebar-offcanvas-left">
    <header id="header-search-sidebar" class="hidden-l">
      <form class="search-sidebar" role="search">
        <input type="text" id="search-sidebar" autocomplete="off" disabled class="form-control typeahead" placeholder="Loading search...">
      </form>
    </header>
    
    <ol class="breadcrumbs gt-separated dark hidden-l" id="sidebar-nav">
      <li><a href="index.html">gms_check</a></li>
      <li><a href="dart-collection/dart-collection-library.html">dart:collection</a></li>
      <li><a href="dart-collection/UnmodifiableListView-class.html">UnmodifiableListView<span class="signature">&lt;<wbr><span class="type-parameter">E</span>&gt;</span></a></li>
      <li class="self-crumb">removeWhere method</li>
    </ol>
    
    <h5>UnmodifiableListView class</h5>
    <ol>
    
        <li class="section-title"><a href="dart-collection/UnmodifiableListView-class.html#constructors">Constructors</a></li>
        <li><a href="dart-collection/UnmodifiableListView/UnmodifiableListView.html">UnmodifiableListView</a></li>
    
        <li class="section-title">
            <a href="dart-collection/UnmodifiableListView-class.html#instance-properties">Properties</a>
        </li>
        <li><a href="dart-collection/UnmodifiableListView/length.html">length</a></li>
        <li class="inherited"><a href="dart-collection/ListMixin/first.html">first</a></li>
        <li class="inherited"><a href="dart-core/Object/hashCode.html">hashCode</a></li>
        <li class="inherited"><a href="dart-collection/ListMixin/isEmpty.html">isEmpty</a></li>
        <li class="inherited"><a href="dart-collection/ListMixin/isNotEmpty.html">isNotEmpty</a></li>
        <li class="inherited"><a href="dart-collection/ListMixin/iterator.html">iterator</a></li>
        <li class="inherited"><a href="dart-collection/ListMixin/last.html">last</a></li>
        <li class="inherited"><a href="dart-collection/ListMixin/reversed.html">reversed</a></li>
        <li class="inherited"><a href="dart-core/Object/runtimeType.html">runtimeType</a></li>
        <li class="inherited"><a href="dart-collection/ListMixin/single.html">single</a></li>
    
        <li class="section-title"><a href="dart-collection/UnmodifiableListView-class.html#instance-methods">Methods</a></li>
        <li><a href="dart-collection/UnmodifiableListView/cast.html">cast</a></li>
        <li class="inherited"><a href="dart-collection/UnmodifiableListView/add.html">add</a></li>
        <li class="inherited"><a href="dart-collection/UnmodifiableListView/addAll.html">addAll</a></li>
        <li class="inherited"><a href="dart-collection/ListMixin/any.html">any</a></li>
        <li class="inherited"><a href="dart-collection/ListMixin/asMap.html">asMap</a></li>
        <li class="inherited"><a href="dart-collection/UnmodifiableListView/clear.html">clear</a></li>
        <li class="inherited"><a href="dart-collection/ListMixin/contains.html">contains</a></li>
        <li class="inherited"><a href="dart-collection/ListMixin/elementAt.html">elementAt</a></li>
        <li class="inherited"><a href="dart-collection/ListMixin/every.html">every</a></li>
        <li class="inherited"><a href="dart-collection/ListMixin/expand.html">expand</a></li>
        <li class="inherited"><a href="dart-collection/UnmodifiableListView/fillRange.html">fillRange</a></li>
        <li class="inherited"><a href="dart-collection/ListMixin/firstWhere.html">firstWhere</a></li>
        <li class="inherited"><a href="dart-collection/ListMixin/fold.html">fold</a></li>
        <li class="inherited"><a href="dart-collection/ListMixin/followedBy.html">followedBy</a></li>
        <li class="inherited"><a href="dart-collection/ListMixin/forEach.html">forEach</a></li>
        <li class="inherited"><a href="dart-collection/ListMixin/getRange.html">getRange</a></li>
        <li class="inherited"><a href="dart-collection/ListMixin/indexOf.html">indexOf</a></li>
        <li class="inherited"><a href="dart-collection/ListMixin/indexWhere.html">indexWhere</a></li>
        <li class="inherited"><a href="dart-collection/UnmodifiableListView/insert.html">insert</a></li>
        <li class="inherited"><a href="dart-collection/UnmodifiableListView/insertAll.html">insertAll</a></li>
        <li class="inherited"><a href="dart-collection/ListMixin/join.html">join</a></li>
        <li class="inherited"><a href="dart-collection/ListMixin/lastIndexOf.html">lastIndexOf</a></li>
        <li class="inherited"><a href="dart-collection/ListMixin/lastIndexWhere.html">lastIndexWhere</a></li>
        <li class="inherited"><a href="dart-collection/ListMixin/lastWhere.html">lastWhere</a></li>
        <li class="inherited"><a href="dart-collection/ListMixin/map.html">map</a></li>
        <li class="inherited"><a href="dart-core/Object/noSuchMethod.html">noSuchMethod</a></li>
        <li class="inherited"><a href="dart-collection/ListMixin/reduce.html">reduce</a></li>
        <li class="inherited"><a href="dart-collection/UnmodifiableListView/remove.html">remove</a></li>
        <li class="inherited"><a href="dart-collection/UnmodifiableListView/removeAt.html">removeAt</a></li>
        <li class="inherited"><a href="dart-collection/UnmodifiableListView/removeLast.html">removeLast</a></li>
        <li class="inherited"><a href="dart-collection/UnmodifiableListView/removeRange.html">removeRange</a></li>
        <li class="inherited"><a href="dart-collection/UnmodifiableListView/removeWhere.html">removeWhere</a></li>
        <li class="inherited"><a href="dart-collection/UnmodifiableListView/replaceRange.html">replaceRange</a></li>
        <li class="inherited"><a href="dart-collection/UnmodifiableListView/retainWhere.html">retainWhere</a></li>
        <li class="inherited"><a href="dart-collection/UnmodifiableListView/setAll.html">setAll</a></li>
        <li class="inherited"><a href="dart-collection/UnmodifiableListView/setRange.html">setRange</a></li>
        <li class="inherited"><a href="dart-collection/UnmodifiableListView/shuffle.html">shuffle</a></li>
        <li class="inherited"><a href="dart-collection/ListMixin/singleWhere.html">singleWhere</a></li>
        <li class="inherited"><a href="dart-collection/ListMixin/skip.html">skip</a></li>
        <li class="inherited"><a href="dart-collection/ListMixin/skipWhile.html">skipWhile</a></li>
        <li class="inherited"><a href="dart-collection/UnmodifiableListView/sort.html">sort</a></li>
        <li class="inherited"><a href="dart-collection/ListMixin/sublist.html">sublist</a></li>
        <li class="inherited"><a href="dart-collection/ListMixin/take.html">take</a></li>
        <li class="inherited"><a href="dart-collection/ListMixin/takeWhile.html">takeWhile</a></li>
        <li class="inherited"><a href="dart-collection/ListMixin/toList.html">toList</a></li>
        <li class="inherited"><a href="dart-collection/ListMixin/toSet.html">toSet</a></li>
        <li class="inherited"><a href="dart-collection/ListMixin/toString.html">toString</a></li>
        <li class="inherited"><a href="dart-collection/ListMixin/where.html">where</a></li>
        <li class="inherited"><a href="dart-collection/ListMixin/whereType.html">whereType</a></li>
    
        <li class="section-title"><a href="dart-collection/UnmodifiableListView-class.html#operators">Operators</a></li>
        <li><a href="dart-collection/UnmodifiableListView/operator_get.html">operator []</a></li>
        <li class="inherited"><a href="dart-collection/ListMixin/operator_plus.html">operator +</a></li>
        <li class="inherited"><a href="dart-core/Object/operator_equals.html">operator ==</a></li>
        <li class="inherited"><a href="dart-collection/UnmodifiableListView/operator_put.html">operator []=</a></li>
    
    
    
    
    
    </ol>
  </div><!--/.sidebar-offcanvas-->

  <div id="dartdoc-main-content" class="col-xs-12 col-sm-9 col-md-8 main-content">
      <div><h1><span class="kind-method">removeWhere</span> method</h1></div>

    <section class="multi-line-signature">
      <span class="returntype">void</span>
            <span class="name ">removeWhere</span>
(<wbr><span class="parameter" id="removeWhere-param-test"><span class="type-annotation"><a href="dart-core/bool-class.html">bool</a></span> <span class="parameter-name">test</span>(<span class="parameter" id="param-element"><span class="type-annotation">E</span> <span class="parameter-name">element</span></span>)</span>)
      <div class="features">inherited</div>
    </section>
    <section class="desc markdown">
      <p>This operation is not supported by an unmodifiable list.</p>
    </section>
    
    <section class="summary source-code" id="source">
      <h2><span>Implementation</span></h2>
      <pre class="language-dart"><code class="language-dart">void removeWhere(bool test(E element)) {
  throw new UnsupportedError(&quot;Cannot remove from an unmodifiable list&quot;);
}</code></pre>
    </section>

  </div> <!-- /.main-content -->

  <div id="dartdoc-sidebar-right" class="col-xs-6 col-sm-6 col-md-2 sidebar sidebar-offcanvas-right">
  </div><!--/.sidebar-offcanvas-->

</main>

<footer>
  <span class="no-break">
    gms_check
      1.0.0
  </span>

  <!-- footer-text placeholder -->
</footer>

<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.2.1/jquery.min.js"></script>
<script src="static-assets/typeahead.bundle.min.js"></script>
<script src="static-assets/highlight.pack.js"></script>
<script src="static-assets/URI.js"></script>
<script src="static-assets/script.js"></script>
<!-- footer placeholder -->

</body>

</html>
