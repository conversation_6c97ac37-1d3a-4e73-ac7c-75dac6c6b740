<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, height=device-height, initial-scale=1, user-scalable=no">
  <meta name="description" content="API docs for the IterableMixin constructor from the Class IterableMixin class from the dart:collection library, for the Dart programming language.">
  <title>IterableMixin constructor - IterableMixin class - dart:collection library - Dart API</title>
  <!-- required because all the links are pseudo-absolute -->
  <base href="../..">

  <link href="https://fonts.googleapis.com/css?family=Source+Code+Pro:500,400i,400,300|Source+Sans+Pro:400,300,700" rel="stylesheet">
  <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
  <link rel="stylesheet" href="static-assets/github.css">
  <link rel="stylesheet" href="static-assets/styles.css">
  <link rel="icon" href="static-assets/favicon.png">
  <!-- header placeholder -->
</head>

<body>

<div id="overlay-under-drawer"></div>

<header id="title">
  <button id="sidenav-left-toggle" type="button">&nbsp;</button>
  <ol class="breadcrumbs gt-separated dark hidden-xs">
    <li><a href="index.html">gms_check</a></li>
    <li><a href="dart-collection/dart-collection-library.html">dart:collection</a></li>
    <li><a href="dart-collection/IterableMixin-class.html">IterableMixin<span class="signature">&lt;<wbr><span class="type-parameter">E</span>&gt;</span></a></li>
    <li class="self-crumb">IterableMixin constructor</li>
  </ol>
  <div class="self-name">IterableMixin</div>
  <form class="search navbar-right" role="search">
    <input type="text" id="search-box" autocomplete="off" disabled class="form-control typeahead" placeholder="Loading search...">
  </form>
</header>

<main>

  <div id="dartdoc-sidebar-left" class="col-xs-6 col-sm-3 col-md-2 sidebar sidebar-offcanvas-left">
    <header id="header-search-sidebar" class="hidden-l">
      <form class="search-sidebar" role="search">
        <input type="text" id="search-sidebar" autocomplete="off" disabled class="form-control typeahead" placeholder="Loading search...">
      </form>
    </header>
    
    <ol class="breadcrumbs gt-separated dark hidden-l" id="sidebar-nav">
      <li><a href="index.html">gms_check</a></li>
      <li><a href="dart-collection/dart-collection-library.html">dart:collection</a></li>
      <li><a href="dart-collection/IterableMixin-class.html">IterableMixin<span class="signature">&lt;<wbr><span class="type-parameter">E</span>&gt;</span></a></li>
      <li class="self-crumb">IterableMixin constructor</li>
    </ol>
    
    <h5>IterableMixin class</h5>
    <ol>
    
      <li class="section-title"><a href="dart-collection/IterableMixin-class.html#constructors">Constructors</a></li>
      <li><a href="dart-collection/IterableMixin/IterableMixin.html">IterableMixin</a></li>
    
      <li class="section-title">
        <a href="dart-collection/IterableMixin-class.html#instance-properties">Properties</a>
      </li>
      <li><a href="dart-collection/IterableMixin/first.html">first</a></li>
      <li><a href="dart-collection/IterableMixin/isEmpty.html">isEmpty</a></li>
      <li><a href="dart-collection/IterableMixin/isNotEmpty.html">isNotEmpty</a></li>
      <li><a href="dart-collection/IterableMixin/last.html">last</a></li>
      <li><a href="dart-collection/IterableMixin/length.html">length</a></li>
      <li><a href="dart-collection/IterableMixin/single.html">single</a></li>
      <li class="inherited"><a href="dart-core/Object/hashCode.html">hashCode</a></li>
      <li class="inherited"><a href="dart-core/Iterable/iterator.html">iterator</a></li>
      <li class="inherited"><a href="dart-core/Object/runtimeType.html">runtimeType</a></li>
    
      <li class="section-title"><a href="dart-collection/IterableMixin-class.html#instance-methods">Methods</a></li>
      <li><a href="dart-collection/IterableMixin/any.html">any</a></li>
      <li><a href="dart-collection/IterableMixin/cast.html">cast</a></li>
      <li><a href="dart-collection/IterableMixin/contains.html">contains</a></li>
      <li><a href="dart-collection/IterableMixin/elementAt.html">elementAt</a></li>
      <li><a href="dart-collection/IterableMixin/every.html">every</a></li>
      <li><a href="dart-collection/IterableMixin/expand.html">expand</a></li>
      <li><a href="dart-collection/IterableMixin/firstWhere.html">firstWhere</a></li>
      <li><a href="dart-collection/IterableMixin/fold.html">fold</a></li>
      <li><a href="dart-collection/IterableMixin/followedBy.html">followedBy</a></li>
      <li><a href="dart-collection/IterableMixin/forEach.html">forEach</a></li>
      <li><a href="dart-collection/IterableMixin/join.html">join</a></li>
      <li><a href="dart-collection/IterableMixin/lastWhere.html">lastWhere</a></li>
      <li><a href="dart-collection/IterableMixin/map.html">map</a></li>
      <li><a href="dart-collection/IterableMixin/reduce.html">reduce</a></li>
      <li><a href="dart-collection/IterableMixin/singleWhere.html">singleWhere</a></li>
      <li><a href="dart-collection/IterableMixin/skip.html">skip</a></li>
      <li><a href="dart-collection/IterableMixin/skipWhile.html">skipWhile</a></li>
      <li><a href="dart-collection/IterableMixin/take.html">take</a></li>
      <li><a href="dart-collection/IterableMixin/takeWhile.html">takeWhile</a></li>
      <li><a href="dart-collection/IterableMixin/toList.html">toList</a></li>
      <li><a href="dart-collection/IterableMixin/toSet.html">toSet</a></li>
      <li><a href="dart-collection/IterableMixin/toString.html">toString</a></li>
      <li><a href="dart-collection/IterableMixin/where.html">where</a></li>
      <li><a href="dart-collection/IterableMixin/whereType.html">whereType</a></li>
      <li class="inherited"><a href="dart-core/Object/noSuchMethod.html">noSuchMethod</a></li>
    
      <li class="section-title inherited"><a href="dart-collection/IterableMixin-class.html#operators">Operators</a></li>
      <li class="inherited"><a href="dart-core/Object/operator_equals.html">operator ==</a></li>
    
    
    
    
    </ol>
  </div><!--/.sidebar-offcanvas-left-->

  <div id="dartdoc-main-content" class="col-xs-12 col-sm-9 col-md-8 main-content">
      <div><h1><span class="kind-constructor">IterableMixin&lt;<wbr><span class="type-parameter">E</span>&gt;</span> constructor</h1></div>

    <section class="multi-line-signature">
      
      <span class="name ">IterableMixin&lt;<wbr><span class="type-parameter">E</span>&gt;</span>(<wbr>)
    </section>

    
    

  </div> <!-- /.main-content -->

  <div id="dartdoc-sidebar-right" class="col-xs-6 col-sm-6 col-md-2 sidebar sidebar-offcanvas-right">
  </div><!--/.sidebar-offcanvas-->

</main>

<footer>
  <span class="no-break">
    gms_check
      1.0.0
  </span>

  <!-- footer-text placeholder -->
</footer>

<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.2.1/jquery.min.js"></script>
<script src="static-assets/typeahead.bundle.min.js"></script>
<script src="static-assets/highlight.pack.js"></script>
<script src="static-assets/URI.js"></script>
<script src="static-assets/script.js"></script>
<!-- footer placeholder -->

</body>

</html>
