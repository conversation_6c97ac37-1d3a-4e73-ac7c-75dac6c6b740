<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, height=device-height, initial-scale=1, user-scalable=no">
  <meta name="description" content="API docs for the expand method from the IterableMixin class, for the Dart programming language.">
  <title>expand method - IterableMixin class - dart:collection library - Dart API</title>
  <!-- required because all the links are pseudo-absolute -->
  <base href="../..">

  <link href="https://fonts.googleapis.com/css?family=Source+Code+Pro:500,400i,400,300|Source+Sans+Pro:400,300,700" rel="stylesheet">
  <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
  <link rel="stylesheet" href="static-assets/github.css">
  <link rel="stylesheet" href="static-assets/styles.css">
  <link rel="icon" href="static-assets/favicon.png">
  <!-- header placeholder -->
</head>

<body>

<div id="overlay-under-drawer"></div>

<header id="title">
  <button id="sidenav-left-toggle" type="button">&nbsp;</button>
  <ol class="breadcrumbs gt-separated dark hidden-xs">
    <li><a href="index.html">gms_check</a></li>
    <li><a href="dart-collection/dart-collection-library.html">dart:collection</a></li>
    <li><a href="dart-collection/IterableMixin-class.html">IterableMixin<span class="signature">&lt;<wbr><span class="type-parameter">E</span>&gt;</span></a></li>
    <li class="self-crumb">expand&lt;<wbr><span class="type-parameter">T</span>&gt; method</li>
  </ol>
  <div class="self-name">expand</div>
  <form class="search navbar-right" role="search">
    <input type="text" id="search-box" autocomplete="off" disabled class="form-control typeahead" placeholder="Loading search...">
  </form>
</header>

<main>

  <div id="dartdoc-sidebar-left" class="col-xs-6 col-sm-3 col-md-2 sidebar sidebar-offcanvas-left">
    <header id="header-search-sidebar" class="hidden-l">
      <form class="search-sidebar" role="search">
        <input type="text" id="search-sidebar" autocomplete="off" disabled class="form-control typeahead" placeholder="Loading search...">
      </form>
    </header>
    
    <ol class="breadcrumbs gt-separated dark hidden-l" id="sidebar-nav">
      <li><a href="index.html">gms_check</a></li>
      <li><a href="dart-collection/dart-collection-library.html">dart:collection</a></li>
      <li><a href="dart-collection/IterableMixin-class.html">IterableMixin<span class="signature">&lt;<wbr><span class="type-parameter">E</span>&gt;</span></a></li>
      <li class="self-crumb">expand&lt;<wbr><span class="type-parameter">T</span>&gt; method</li>
    </ol>
    
    <h5>IterableMixin class</h5>
    <ol>
    
        <li class="section-title"><a href="dart-collection/IterableMixin-class.html#constructors">Constructors</a></li>
        <li><a href="dart-collection/IterableMixin/IterableMixin.html">IterableMixin</a></li>
    
        <li class="section-title">
            <a href="dart-collection/IterableMixin-class.html#instance-properties">Properties</a>
        </li>
        <li><a href="dart-collection/IterableMixin/first.html">first</a></li>
        <li><a href="dart-collection/IterableMixin/isEmpty.html">isEmpty</a></li>
        <li><a href="dart-collection/IterableMixin/isNotEmpty.html">isNotEmpty</a></li>
        <li><a href="dart-collection/IterableMixin/last.html">last</a></li>
        <li><a href="dart-collection/IterableMixin/length.html">length</a></li>
        <li><a href="dart-collection/IterableMixin/single.html">single</a></li>
        <li class="inherited"><a href="dart-core/Object/hashCode.html">hashCode</a></li>
        <li class="inherited"><a href="dart-core/Iterable/iterator.html">iterator</a></li>
        <li class="inherited"><a href="dart-core/Object/runtimeType.html">runtimeType</a></li>
    
        <li class="section-title"><a href="dart-collection/IterableMixin-class.html#instance-methods">Methods</a></li>
        <li><a href="dart-collection/IterableMixin/any.html">any</a></li>
        <li><a href="dart-collection/IterableMixin/cast.html">cast</a></li>
        <li><a href="dart-collection/IterableMixin/contains.html">contains</a></li>
        <li><a href="dart-collection/IterableMixin/elementAt.html">elementAt</a></li>
        <li><a href="dart-collection/IterableMixin/every.html">every</a></li>
        <li><a href="dart-collection/IterableMixin/expand.html">expand</a></li>
        <li><a href="dart-collection/IterableMixin/firstWhere.html">firstWhere</a></li>
        <li><a href="dart-collection/IterableMixin/fold.html">fold</a></li>
        <li><a href="dart-collection/IterableMixin/followedBy.html">followedBy</a></li>
        <li><a href="dart-collection/IterableMixin/forEach.html">forEach</a></li>
        <li><a href="dart-collection/IterableMixin/join.html">join</a></li>
        <li><a href="dart-collection/IterableMixin/lastWhere.html">lastWhere</a></li>
        <li><a href="dart-collection/IterableMixin/map.html">map</a></li>
        <li><a href="dart-collection/IterableMixin/reduce.html">reduce</a></li>
        <li><a href="dart-collection/IterableMixin/singleWhere.html">singleWhere</a></li>
        <li><a href="dart-collection/IterableMixin/skip.html">skip</a></li>
        <li><a href="dart-collection/IterableMixin/skipWhile.html">skipWhile</a></li>
        <li><a href="dart-collection/IterableMixin/take.html">take</a></li>
        <li><a href="dart-collection/IterableMixin/takeWhile.html">takeWhile</a></li>
        <li><a href="dart-collection/IterableMixin/toList.html">toList</a></li>
        <li><a href="dart-collection/IterableMixin/toSet.html">toSet</a></li>
        <li><a href="dart-collection/IterableMixin/toString.html">toString</a></li>
        <li><a href="dart-collection/IterableMixin/where.html">where</a></li>
        <li><a href="dart-collection/IterableMixin/whereType.html">whereType</a></li>
        <li class="inherited"><a href="dart-core/Object/noSuchMethod.html">noSuchMethod</a></li>
    
        <li class="section-title inherited"><a href="dart-collection/IterableMixin-class.html#operators">Operators</a></li>
        <li class="inherited"><a href="dart-core/Object/operator_equals.html">operator ==</a></li>
    
    
    
    
    
    </ol>
  </div><!--/.sidebar-offcanvas-->

  <div id="dartdoc-main-content" class="col-xs-12 col-sm-9 col-md-8 main-content">
      <div><h1><span class="kind-method">expand&lt;<wbr><span class="type-parameter">T</span>&gt;</span> method</h1></div>

    <section class="multi-line-signature">
      <span class="returntype"><a href="dart-core/Iterable-class.html">Iterable</a><span class="signature">&lt;<wbr><span class="type-parameter">T</span>&gt;</span></span>
            <span class="name ">expand</span>
&lt;<wbr><span class="type-parameter">T</span>&gt;(<wbr><span class="parameter" id="expand-param-toElements"><span class="type-annotation"><a href="dart-core/Iterable-class.html">Iterable</a><span class="signature">&lt;<wbr><span class="type-parameter">T</span>&gt;</span></span> <span class="parameter-name">toElements</span>(<span class="parameter" id="param-element"><span class="type-annotation">E</span> <span class="parameter-name">element</span></span>)</span>)
      <div class="features">override</div>
    </section>
    <section class="desc markdown">
      <p>Expands each element of this <a href="dart-core/Iterable-class.html">Iterable</a> into zero or more elements.</p>
<p>The resulting Iterable runs through the elements returned
by <code>toElements</code> for each element of this, in iteration order.</p>
<p>The returned <a href="dart-core/Iterable-class.html">Iterable</a> is lazy, and calls <code>toElements</code> for each element
of this iterable every time the returned iterable is iterated.</p>
<p>Example:</p>
<pre class="language-dart"><code class="language-dart">var pairs = [[1, 2], [3, 4]];
var flattened = pairs.expand((pair) =&gt; pair).toList();
print(flattened); // =&gt; [1, 2, 3, 4];

var input = [1, 2, 3];
var duplicated = input.expand((i) =&gt; [i, i]).toList();
print(duplicated); // =&gt; [1, 1, 2, 2, 3, 3]
</code></pre>
<p>Equivalent to:</p>
<pre class="language-dart"><code class="language-dart">Iterable&lt;T&gt; expand&lt;T&gt;(Iterable&lt;T&gt; toElements(E e)) sync* {
  for (var value in this) {
    yield* toElements(value);
  }
}
</code></pre>
    </section>
    
    <section class="summary source-code" id="source">
      <h2><span>Implementation</span></h2>
      <pre class="language-dart"><code class="language-dart">Iterable&lt;T&gt; expand&lt;T&gt;(Iterable&lt;T&gt; toElements(E element)) =&gt;
    ExpandIterable&lt;E, T&gt;(this, toElements);</code></pre>
    </section>

  </div> <!-- /.main-content -->

  <div id="dartdoc-sidebar-right" class="col-xs-6 col-sm-6 col-md-2 sidebar sidebar-offcanvas-right">
  </div><!--/.sidebar-offcanvas-->

</main>

<footer>
  <span class="no-break">
    gms_check
      1.0.0
  </span>

  <!-- footer-text placeholder -->
</footer>

<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.2.1/jquery.min.js"></script>
<script src="static-assets/typeahead.bundle.min.js"></script>
<script src="static-assets/highlight.pack.js"></script>
<script src="static-assets/URI.js"></script>
<script src="static-assets/script.js"></script>
<!-- footer placeholder -->

</body>

</html>
