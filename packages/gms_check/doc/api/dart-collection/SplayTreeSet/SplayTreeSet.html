<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, height=device-height, initial-scale=1, user-scalable=no">
  <meta name="description" content="API docs for the SplayTreeSet constructor from the Class SplayTreeSet class from the dart:collection library, for the Dart programming language.">
  <title>SplayTreeSet constructor - SplayTreeSet class - dart:collection library - Dart API</title>
  <!-- required because all the links are pseudo-absolute -->
  <base href="../..">

  <link href="https://fonts.googleapis.com/css?family=Source+Code+Pro:500,400i,400,300|Source+Sans+Pro:400,300,700" rel="stylesheet">
  <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
  <link rel="stylesheet" href="static-assets/github.css">
  <link rel="stylesheet" href="static-assets/styles.css">
  <link rel="icon" href="static-assets/favicon.png">
  <!-- header placeholder -->
</head>

<body>

<div id="overlay-under-drawer"></div>

<header id="title">
  <button id="sidenav-left-toggle" type="button">&nbsp;</button>
  <ol class="breadcrumbs gt-separated dark hidden-xs">
    <li><a href="index.html">gms_check</a></li>
    <li><a href="dart-collection/dart-collection-library.html">dart:collection</a></li>
    <li><a href="dart-collection/SplayTreeSet-class.html">SplayTreeSet<span class="signature">&lt;<wbr><span class="type-parameter">E</span>&gt;</span></a></li>
    <li class="self-crumb">SplayTreeSet constructor</li>
  </ol>
  <div class="self-name">SplayTreeSet</div>
  <form class="search navbar-right" role="search">
    <input type="text" id="search-box" autocomplete="off" disabled class="form-control typeahead" placeholder="Loading search...">
  </form>
</header>

<main>

  <div id="dartdoc-sidebar-left" class="col-xs-6 col-sm-3 col-md-2 sidebar sidebar-offcanvas-left">
    <header id="header-search-sidebar" class="hidden-l">
      <form class="search-sidebar" role="search">
        <input type="text" id="search-sidebar" autocomplete="off" disabled class="form-control typeahead" placeholder="Loading search...">
      </form>
    </header>
    
    <ol class="breadcrumbs gt-separated dark hidden-l" id="sidebar-nav">
      <li><a href="index.html">gms_check</a></li>
      <li><a href="dart-collection/dart-collection-library.html">dart:collection</a></li>
      <li><a href="dart-collection/SplayTreeSet-class.html">SplayTreeSet<span class="signature">&lt;<wbr><span class="type-parameter">E</span>&gt;</span></a></li>
      <li class="self-crumb">SplayTreeSet constructor</li>
    </ol>
    
    <h5>SplayTreeSet class</h5>
    <ol>
    
      <li class="section-title"><a href="dart-collection/SplayTreeSet-class.html#constructors">Constructors</a></li>
      <li><a href="dart-collection/SplayTreeSet/SplayTreeSet.html">SplayTreeSet</a></li>
      <li><a href="dart-collection/SplayTreeSet/SplayTreeSet.from.html">from</a></li>
      <li><a href="dart-collection/SplayTreeSet/SplayTreeSet.of.html">of</a></li>
    
      <li class="section-title">
        <a href="dart-collection/SplayTreeSet-class.html#instance-properties">Properties</a>
      </li>
      <li><a href="dart-collection/SplayTreeSet/first.html">first</a></li>
      <li><a href="dart-collection/SplayTreeSet/isEmpty.html">isEmpty</a></li>
      <li><a href="dart-collection/SplayTreeSet/isNotEmpty.html">isNotEmpty</a></li>
      <li><a href="dart-collection/SplayTreeSet/iterator.html">iterator</a></li>
      <li><a href="dart-collection/SplayTreeSet/last.html">last</a></li>
      <li><a href="dart-collection/SplayTreeSet/length.html">length</a></li>
      <li><a href="dart-collection/SplayTreeSet/single.html">single</a></li>
      <li class="inherited"><a href="dart-core/Object/hashCode.html">hashCode</a></li>
      <li class="inherited"><a href="dart-core/Object/runtimeType.html">runtimeType</a></li>
    
      <li class="section-title"><a href="dart-collection/SplayTreeSet-class.html#instance-methods">Methods</a></li>
      <li><a href="dart-collection/SplayTreeSet/add.html">add</a></li>
      <li><a href="dart-collection/SplayTreeSet/addAll.html">addAll</a></li>
      <li><a href="dart-collection/SplayTreeSet/cast.html">cast</a></li>
      <li><a href="dart-collection/SplayTreeSet/clear.html">clear</a></li>
      <li><a href="dart-collection/SplayTreeSet/contains.html">contains</a></li>
      <li><a href="dart-collection/SplayTreeSet/difference.html">difference</a></li>
      <li><a href="dart-collection/SplayTreeSet/intersection.html">intersection</a></li>
      <li><a href="dart-collection/SplayTreeSet/lookup.html">lookup</a></li>
      <li><a href="dart-collection/SplayTreeSet/remove.html">remove</a></li>
      <li><a href="dart-collection/SplayTreeSet/removeAll.html">removeAll</a></li>
      <li><a href="dart-collection/SplayTreeSet/retainAll.html">retainAll</a></li>
      <li><a href="dart-collection/SplayTreeSet/toSet.html">toSet</a></li>
      <li><a href="dart-collection/SplayTreeSet/toString.html">toString</a></li>
      <li><a href="dart-collection/SplayTreeSet/union.html">union</a></li>
      <li class="inherited"><a href="dart-collection/SetMixin/any.html">any</a></li>
      <li class="inherited"><a href="dart-collection/SetMixin/containsAll.html">containsAll</a></li>
      <li class="inherited"><a href="dart-collection/SetMixin/elementAt.html">elementAt</a></li>
      <li class="inherited"><a href="dart-collection/SetMixin/every.html">every</a></li>
      <li class="inherited"><a href="dart-collection/SetMixin/expand.html">expand</a></li>
      <li class="inherited"><a href="dart-collection/SetMixin/firstWhere.html">firstWhere</a></li>
      <li class="inherited"><a href="dart-collection/SetMixin/fold.html">fold</a></li>
      <li class="inherited"><a href="dart-collection/SetMixin/followedBy.html">followedBy</a></li>
      <li class="inherited"><a href="dart-collection/SetMixin/forEach.html">forEach</a></li>
      <li class="inherited"><a href="dart-collection/SetMixin/join.html">join</a></li>
      <li class="inherited"><a href="dart-collection/SetMixin/lastWhere.html">lastWhere</a></li>
      <li class="inherited"><a href="dart-collection/SetMixin/map.html">map</a></li>
      <li class="inherited"><a href="dart-core/Object/noSuchMethod.html">noSuchMethod</a></li>
      <li class="inherited"><a href="dart-collection/SetMixin/reduce.html">reduce</a></li>
      <li class="inherited"><a href="dart-collection/SetMixin/removeWhere.html">removeWhere</a></li>
      <li class="inherited"><a href="dart-collection/SetMixin/retainWhere.html">retainWhere</a></li>
      <li class="inherited"><a href="dart-collection/SetMixin/singleWhere.html">singleWhere</a></li>
      <li class="inherited"><a href="dart-collection/SetMixin/skip.html">skip</a></li>
      <li class="inherited"><a href="dart-collection/SetMixin/skipWhile.html">skipWhile</a></li>
      <li class="inherited"><a href="dart-collection/SetMixin/take.html">take</a></li>
      <li class="inherited"><a href="dart-collection/SetMixin/takeWhile.html">takeWhile</a></li>
      <li class="inherited"><a href="dart-collection/SetMixin/toList.html">toList</a></li>
      <li class="inherited"><a href="dart-collection/SetMixin/where.html">where</a></li>
      <li class="inherited"><a href="dart-collection/SetMixin/whereType.html">whereType</a></li>
    
      <li class="section-title inherited"><a href="dart-collection/SplayTreeSet-class.html#operators">Operators</a></li>
      <li class="inherited"><a href="dart-core/Object/operator_equals.html">operator ==</a></li>
    
    
    
    
    </ol>
  </div><!--/.sidebar-offcanvas-left-->

  <div id="dartdoc-main-content" class="col-xs-12 col-sm-9 col-md-8 main-content">
      <div><h1><span class="kind-constructor">SplayTreeSet&lt;<wbr><span class="type-parameter">E</span>&gt;</span> constructor</h1></div>

    <section class="multi-line-signature">
      
      <span class="name ">SplayTreeSet&lt;<wbr><span class="type-parameter">E</span>&gt;</span>(<wbr>[<span class="parameter" id="-param-compare"><span class="type-annotation"><a href="dart-core/int-class.html">int</a></span> <span class="parameter-name">compare</span>(<span class="parameter" id="param-key1"><span class="type-annotation">E</span> <span class="parameter-name">key1</span></span> <span class="parameter" id="param-key2"><span class="type-annotation">E</span> <span class="parameter-name">key2</span></span>), </span> <span class="parameter" id="-param-isValidKey"><span class="type-annotation"><a href="dart-core/bool-class.html">bool</a></span> <span class="parameter-name">isValidKey</span>(<span class="parameter" id="param-potentialKey"><span class="type-annotation">dynamic</span> <span class="parameter-name">potentialKey</span></span>)</span> ])
    </section>

    <section class="desc markdown">
      <p>Create a new <a href="dart-collection/SplayTreeSet-class.html">SplayTreeSet</a> with the given compare function.</p>
<p>If the <code>compare</code> function is omitted, it defaults to <a href="dart-core/Comparable/compare.html">Comparable.compare</a>,
and the elements must be comparable.</p>
<p>A provided <code>compare</code> function may not work on all objects. It may not even
work on all <code>E</code> instances.</p>
<p>For operations that add elements to the set, the user is supposed to not
pass in objects that doesn't work with the compare function.</p>
<p>The methods <a href="dart-collection/SplayTreeSet/contains.html">contains</a>, <a href="dart-collection/SplayTreeSet/remove.html">remove</a>, <a href="dart-collection/SplayTreeSet/lookup.html">lookup</a>, <a href="dart-collection/SplayTreeSet/removeAll.html">removeAll</a> or <a href="dart-collection/SplayTreeSet/retainAll.html">retainAll</a>
are typed to accept any object(s), and the <code>isValidKey</code> test can used to
filter those objects before handing them to the <code>compare</code> function.</p>
<p>If <code>isValidKey</code> is provided, only values satisfying <code>isValidKey(other)</code>
are compared using the <code>compare</code> method in the methods mentioned above.
If the <code>isValidKey</code> function returns false for an object, it is assumed to
not be in the set.</p>
<p>If omitted, the <code>isValidKey</code> function defaults to checking against the
type parameter: <code>other is E</code>.</p>
    </section>
    
    <section class="summary source-code" id="source">
      <h2><span>Implementation</span></h2>
      <pre class="language-dart"><code class="language-dart">SplayTreeSet(
    [int Function(E key1, E key2)? compare,
    bool Function(dynamic potentialKey)? isValidKey])
    : _compare = compare ?? _defaultCompare&lt;E&gt;(),
      _validKey = isValidKey ?? ((dynamic v) =&gt; v is E);</code></pre>
    </section>

  </div> <!-- /.main-content -->

  <div id="dartdoc-sidebar-right" class="col-xs-6 col-sm-6 col-md-2 sidebar sidebar-offcanvas-right">
  </div><!--/.sidebar-offcanvas-->

</main>

<footer>
  <span class="no-break">
    gms_check
      1.0.0
  </span>

  <!-- footer-text placeholder -->
</footer>

<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.2.1/jquery.min.js"></script>
<script src="static-assets/typeahead.bundle.min.js"></script>
<script src="static-assets/highlight.pack.js"></script>
<script src="static-assets/URI.js"></script>
<script src="static-assets/script.js"></script>
<!-- footer placeholder -->

</body>

</html>
