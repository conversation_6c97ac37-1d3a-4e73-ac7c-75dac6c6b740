<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, height=device-height, initial-scale=1, user-scalable=no">
  <meta name="description" content="API docs for the HashMap.fromIterable constructor from the Class HashMap class from the dart:collection library, for the Dart programming language.">
  <title>HashMap.fromIterable constructor - HashMap class - dart:collection library - Dart API</title>
  <!-- required because all the links are pseudo-absolute -->
  <base href="../..">

  <link href="https://fonts.googleapis.com/css?family=Source+Code+Pro:500,400i,400,300|Source+Sans+Pro:400,300,700" rel="stylesheet">
  <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
  <link rel="stylesheet" href="static-assets/github.css">
  <link rel="stylesheet" href="static-assets/styles.css">
  <link rel="icon" href="static-assets/favicon.png">
  <!-- header placeholder -->
</head>

<body>

<div id="overlay-under-drawer"></div>

<header id="title">
  <button id="sidenav-left-toggle" type="button">&nbsp;</button>
  <ol class="breadcrumbs gt-separated dark hidden-xs">
    <li><a href="index.html">gms_check</a></li>
    <li><a href="dart-collection/dart-collection-library.html">dart:collection</a></li>
    <li><a href="dart-collection/HashMap-class.html">HashMap<span class="signature">&lt;<wbr><span class="type-parameter">K</span>, <span class="type-parameter">V</span>&gt;</span></a></li>
    <li class="self-crumb">HashMap.fromIterable factory constructor</li>
  </ol>
  <div class="self-name">HashMap.fromIterable</div>
  <form class="search navbar-right" role="search">
    <input type="text" id="search-box" autocomplete="off" disabled class="form-control typeahead" placeholder="Loading search...">
  </form>
</header>

<main>

  <div id="dartdoc-sidebar-left" class="col-xs-6 col-sm-3 col-md-2 sidebar sidebar-offcanvas-left">
    <header id="header-search-sidebar" class="hidden-l">
      <form class="search-sidebar" role="search">
        <input type="text" id="search-sidebar" autocomplete="off" disabled class="form-control typeahead" placeholder="Loading search...">
      </form>
    </header>
    
    <ol class="breadcrumbs gt-separated dark hidden-l" id="sidebar-nav">
      <li><a href="index.html">gms_check</a></li>
      <li><a href="dart-collection/dart-collection-library.html">dart:collection</a></li>
      <li><a href="dart-collection/HashMap-class.html">HashMap<span class="signature">&lt;<wbr><span class="type-parameter">K</span>, <span class="type-parameter">V</span>&gt;</span></a></li>
      <li class="self-crumb">HashMap.fromIterable factory constructor</li>
    </ol>
    
    <h5>HashMap class</h5>
    <ol>
    
      <li class="section-title"><a href="dart-collection/HashMap-class.html#constructors">Constructors</a></li>
      <li><a href="dart-collection/HashMap/HashMap.html">HashMap</a></li>
      <li><a href="dart-collection/HashMap/HashMap.from.html">from</a></li>
      <li><a href="dart-collection/HashMap/HashMap.fromEntries.html">fromEntries</a></li>
      <li><a href="dart-collection/HashMap/HashMap.fromIterable.html">fromIterable</a></li>
      <li><a href="dart-collection/HashMap/HashMap.fromIterables.html">fromIterables</a></li>
      <li><a href="dart-collection/HashMap/HashMap.identity.html">identity</a></li>
      <li><a href="dart-collection/HashMap/HashMap.of.html">of</a></li>
    
      <li class="section-title inherited">
        <a href="dart-collection/HashMap-class.html#instance-properties">Properties</a>
      </li>
      <li class="inherited"><a href="dart-core/Map/entries.html">entries</a></li>
      <li class="inherited"><a href="dart-core/Object/hashCode.html">hashCode</a></li>
      <li class="inherited"><a href="dart-core/Map/isEmpty.html">isEmpty</a></li>
      <li class="inherited"><a href="dart-core/Map/isNotEmpty.html">isNotEmpty</a></li>
      <li class="inherited"><a href="dart-core/Map/keys.html">keys</a></li>
      <li class="inherited"><a href="dart-core/Map/length.html">length</a></li>
      <li class="inherited"><a href="dart-core/Object/runtimeType.html">runtimeType</a></li>
      <li class="inherited"><a href="dart-core/Map/values.html">values</a></li>
    
      <li class="section-title inherited"><a href="dart-collection/HashMap-class.html#instance-methods">Methods</a></li>
      <li class="inherited"><a href="dart-core/Map/addAll.html">addAll</a></li>
      <li class="inherited"><a href="dart-core/Map/addEntries.html">addEntries</a></li>
      <li class="inherited"><a href="dart-core/Map/cast.html">cast</a></li>
      <li class="inherited"><a href="dart-core/Map/clear.html">clear</a></li>
      <li class="inherited"><a href="dart-core/Map/containsKey.html">containsKey</a></li>
      <li class="inherited"><a href="dart-core/Map/containsValue.html">containsValue</a></li>
      <li class="inherited"><a href="dart-core/Map/forEach.html">forEach</a></li>
      <li class="inherited"><a href="dart-core/Map/map.html">map</a></li>
      <li class="inherited"><a href="dart-core/Object/noSuchMethod.html">noSuchMethod</a></li>
      <li class="inherited"><a href="dart-core/Map/putIfAbsent.html">putIfAbsent</a></li>
      <li class="inherited"><a href="dart-core/Map/remove.html">remove</a></li>
      <li class="inherited"><a href="dart-core/Map/removeWhere.html">removeWhere</a></li>
      <li class="inherited"><a href="dart-core/Object/toString.html">toString</a></li>
      <li class="inherited"><a href="dart-core/Map/update.html">update</a></li>
      <li class="inherited"><a href="dart-core/Map/updateAll.html">updateAll</a></li>
    
      <li class="section-title inherited"><a href="dart-collection/HashMap-class.html#operators">Operators</a></li>
      <li class="inherited"><a href="dart-core/Object/operator_equals.html">operator ==</a></li>
      <li class="inherited"><a href="dart-core/Map/operator_get.html">operator []</a></li>
      <li class="inherited"><a href="dart-core/Map/operator_put.html">operator []=</a></li>
    
    
    
    
    </ol>
  </div><!--/.sidebar-offcanvas-left-->

  <div id="dartdoc-main-content" class="col-xs-12 col-sm-9 col-md-8 main-content">
      <div><h1><span class="kind-constructor">HashMap&lt;<wbr><span class="type-parameter">K</span>, <span class="type-parameter">V</span>&gt;.fromIterable</span> constructor</h1></div>

    <section class="multi-line-signature">
      
      <span class="name ">HashMap&lt;<wbr><span class="type-parameter">K</span>, <span class="type-parameter">V</span>&gt;.fromIterable</span>(<wbr><span class="parameter" id="fromIterable-param-iterable"><span class="type-annotation"><a href="dart-core/Iterable-class.html">Iterable</a></span> <span class="parameter-name">iterable</span>, {</span> <span class="parameter" id="fromIterable-param-key"><span class="type-annotation">K</span> <span class="parameter-name">key</span>(<span class="parameter" id="param-element"><span class="type-annotation">dynamic</span> <span class="parameter-name">element</span></span>), </span> <span class="parameter" id="fromIterable-param-value"><span class="type-annotation">V</span> <span class="parameter-name">value</span>(<span class="parameter" id="param-element"><span class="type-annotation">dynamic</span> <span class="parameter-name">element</span></span>)</span> })
    </section>

    <section class="desc markdown">
      <p>Creates a <a href="dart-collection/HashMap-class.html">HashMap</a> where the keys and values are computed from the
<code>iterable</code>.</p>
<p>For each element of the <code>iterable</code> this constructor computes a key/value
pair, by applying <code>key</code> and <code>value</code> respectively.</p>
<p>The keys of the key/value pairs do not need to be unique. The last
occurrence of a key will simply overwrite any previous value.</p>
<p>If no values are specified for <code>key</code> and <code>value</code> the default is the
identity function.</p>
    </section>
    
    <section class="summary source-code" id="source">
      <h2><span>Implementation</span></h2>
      <pre class="language-dart"><code class="language-dart">factory HashMap.fromIterable(Iterable iterable,
    {K Function(dynamic element)? key, V Function(dynamic element)? value}) {
  HashMap&lt;K, V&gt; map = HashMap&lt;K, V&gt;();
  MapBase._fillMapWithMappedIterable(map, iterable, key, value);
  return map;
}</code></pre>
    </section>

  </div> <!-- /.main-content -->

  <div id="dartdoc-sidebar-right" class="col-xs-6 col-sm-6 col-md-2 sidebar sidebar-offcanvas-right">
  </div><!--/.sidebar-offcanvas-->

</main>

<footer>
  <span class="no-break">
    gms_check
      1.0.0
  </span>

  <!-- footer-text placeholder -->
</footer>

<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.2.1/jquery.min.js"></script>
<script src="static-assets/typeahead.bundle.min.js"></script>
<script src="static-assets/highlight.pack.js"></script>
<script src="static-assets/URI.js"></script>
<script src="static-assets/script.js"></script>
<!-- footer placeholder -->

</body>

</html>
