<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, height=device-height, initial-scale=1, user-scalable=no">
  <meta name="description" content="API docs for the DoubleLinkedQueue.of constructor from the Class DoubleLinkedQueue class from the dart:collection library, for the Dart programming language.">
  <title>DoubleLinkedQueue.of constructor - DoubleLinkedQueue class - dart:collection library - Dart API</title>
  <!-- required because all the links are pseudo-absolute -->
  <base href="../..">

  <link href="https://fonts.googleapis.com/css?family=Source+Code+Pro:500,400i,400,300|Source+Sans+Pro:400,300,700" rel="stylesheet">
  <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
  <link rel="stylesheet" href="static-assets/github.css">
  <link rel="stylesheet" href="static-assets/styles.css">
  <link rel="icon" href="static-assets/favicon.png">
  <!-- header placeholder -->
</head>

<body>

<div id="overlay-under-drawer"></div>

<header id="title">
  <button id="sidenav-left-toggle" type="button">&nbsp;</button>
  <ol class="breadcrumbs gt-separated dark hidden-xs">
    <li><a href="index.html">gms_check</a></li>
    <li><a href="dart-collection/dart-collection-library.html">dart:collection</a></li>
    <li><a href="dart-collection/DoubleLinkedQueue-class.html">DoubleLinkedQueue<span class="signature">&lt;<wbr><span class="type-parameter">E</span>&gt;</span></a></li>
    <li class="self-crumb">DoubleLinkedQueue.of factory constructor</li>
  </ol>
  <div class="self-name">DoubleLinkedQueue.of</div>
  <form class="search navbar-right" role="search">
    <input type="text" id="search-box" autocomplete="off" disabled class="form-control typeahead" placeholder="Loading search...">
  </form>
</header>

<main>

  <div id="dartdoc-sidebar-left" class="col-xs-6 col-sm-3 col-md-2 sidebar sidebar-offcanvas-left">
    <header id="header-search-sidebar" class="hidden-l">
      <form class="search-sidebar" role="search">
        <input type="text" id="search-sidebar" autocomplete="off" disabled class="form-control typeahead" placeholder="Loading search...">
      </form>
    </header>
    
    <ol class="breadcrumbs gt-separated dark hidden-l" id="sidebar-nav">
      <li><a href="index.html">gms_check</a></li>
      <li><a href="dart-collection/dart-collection-library.html">dart:collection</a></li>
      <li><a href="dart-collection/DoubleLinkedQueue-class.html">DoubleLinkedQueue<span class="signature">&lt;<wbr><span class="type-parameter">E</span>&gt;</span></a></li>
      <li class="self-crumb">DoubleLinkedQueue.of factory constructor</li>
    </ol>
    
    <h5>DoubleLinkedQueue class</h5>
    <ol>
    
      <li class="section-title"><a href="dart-collection/DoubleLinkedQueue-class.html#constructors">Constructors</a></li>
      <li><a href="dart-collection/DoubleLinkedQueue/DoubleLinkedQueue.html">DoubleLinkedQueue</a></li>
      <li><a href="dart-collection/DoubleLinkedQueue/DoubleLinkedQueue.from.html">from</a></li>
      <li><a href="dart-collection/DoubleLinkedQueue/DoubleLinkedQueue.of.html">of</a></li>
    
      <li class="section-title">
        <a href="dart-collection/DoubleLinkedQueue-class.html#instance-properties">Properties</a>
      </li>
      <li><a href="dart-collection/DoubleLinkedQueue/first.html">first</a></li>
      <li><a href="dart-collection/DoubleLinkedQueue/isEmpty.html">isEmpty</a></li>
      <li><a href="dart-collection/DoubleLinkedQueue/iterator.html">iterator</a></li>
      <li><a href="dart-collection/DoubleLinkedQueue/last.html">last</a></li>
      <li><a href="dart-collection/DoubleLinkedQueue/length.html">length</a></li>
      <li><a href="dart-collection/DoubleLinkedQueue/single.html">single</a></li>
      <li class="inherited"><a href="dart-core/Object/hashCode.html">hashCode</a></li>
      <li class="inherited"><a href="dart-core/Iterable/isNotEmpty.html">isNotEmpty</a></li>
      <li class="inherited"><a href="dart-core/Object/runtimeType.html">runtimeType</a></li>
    
      <li class="section-title"><a href="dart-collection/DoubleLinkedQueue-class.html#instance-methods">Methods</a></li>
      <li><a href="dart-collection/DoubleLinkedQueue/add.html">add</a></li>
      <li><a href="dart-collection/DoubleLinkedQueue/addAll.html">addAll</a></li>
      <li><a href="dart-collection/DoubleLinkedQueue/addFirst.html">addFirst</a></li>
      <li><a href="dart-collection/DoubleLinkedQueue/addLast.html">addLast</a></li>
      <li><a href="dart-collection/DoubleLinkedQueue/cast.html">cast</a></li>
      <li><a href="dart-collection/DoubleLinkedQueue/clear.html">clear</a></li>
      <li><a href="dart-collection/DoubleLinkedQueue/firstEntry.html">firstEntry</a></li>
      <li><a href="dart-collection/DoubleLinkedQueue/forEachEntry.html">forEachEntry</a></li>
      <li><a href="dart-collection/DoubleLinkedQueue/lastEntry.html">lastEntry</a></li>
      <li><a href="dart-collection/DoubleLinkedQueue/remove.html">remove</a></li>
      <li><a href="dart-collection/DoubleLinkedQueue/removeFirst.html">removeFirst</a></li>
      <li><a href="dart-collection/DoubleLinkedQueue/removeLast.html">removeLast</a></li>
      <li><a href="dart-collection/DoubleLinkedQueue/removeWhere.html">removeWhere</a></li>
      <li><a href="dart-collection/DoubleLinkedQueue/retainWhere.html">retainWhere</a></li>
      <li><a href="dart-collection/DoubleLinkedQueue/toString.html">toString</a></li>
      <li class="inherited"><a href="dart-core/Iterable/any.html">any</a></li>
      <li class="inherited"><a href="dart-core/Iterable/contains.html">contains</a></li>
      <li class="inherited"><a href="dart-core/Iterable/elementAt.html">elementAt</a></li>
      <li class="inherited"><a href="dart-core/Iterable/every.html">every</a></li>
      <li class="inherited"><a href="dart-core/Iterable/expand.html">expand</a></li>
      <li class="inherited"><a href="dart-core/Iterable/firstWhere.html">firstWhere</a></li>
      <li class="inherited"><a href="dart-core/Iterable/fold.html">fold</a></li>
      <li class="inherited"><a href="dart-core/Iterable/followedBy.html">followedBy</a></li>
      <li class="inherited"><a href="dart-core/Iterable/forEach.html">forEach</a></li>
      <li class="inherited"><a href="dart-core/Iterable/join.html">join</a></li>
      <li class="inherited"><a href="dart-core/Iterable/lastWhere.html">lastWhere</a></li>
      <li class="inherited"><a href="dart-core/Iterable/map.html">map</a></li>
      <li class="inherited"><a href="dart-core/Object/noSuchMethod.html">noSuchMethod</a></li>
      <li class="inherited"><a href="dart-core/Iterable/reduce.html">reduce</a></li>
      <li class="inherited"><a href="dart-core/Iterable/singleWhere.html">singleWhere</a></li>
      <li class="inherited"><a href="dart-core/Iterable/skip.html">skip</a></li>
      <li class="inherited"><a href="dart-core/Iterable/skipWhile.html">skipWhile</a></li>
      <li class="inherited"><a href="dart-core/Iterable/take.html">take</a></li>
      <li class="inherited"><a href="dart-core/Iterable/takeWhile.html">takeWhile</a></li>
      <li class="inherited"><a href="dart-core/Iterable/toList.html">toList</a></li>
      <li class="inherited"><a href="dart-core/Iterable/toSet.html">toSet</a></li>
      <li class="inherited"><a href="dart-core/Iterable/where.html">where</a></li>
      <li class="inherited"><a href="dart-core/Iterable/whereType.html">whereType</a></li>
    
      <li class="section-title inherited"><a href="dart-collection/DoubleLinkedQueue-class.html#operators">Operators</a></li>
      <li class="inherited"><a href="dart-core/Object/operator_equals.html">operator ==</a></li>
    
    
    
    
    </ol>
  </div><!--/.sidebar-offcanvas-left-->

  <div id="dartdoc-main-content" class="col-xs-12 col-sm-9 col-md-8 main-content">
      <div><h1><span class="kind-constructor">DoubleLinkedQueue&lt;<wbr><span class="type-parameter">E</span>&gt;.of</span> constructor</h1></div>

    <section class="multi-line-signature">
      
      <span class="name ">DoubleLinkedQueue&lt;<wbr><span class="type-parameter">E</span>&gt;.of</span>(<wbr><span class="parameter" id="of-param-elements"><span class="type-annotation"><a href="dart-core/Iterable-class.html">Iterable</a><span class="signature">&lt;<wbr><span class="type-parameter">E</span>&gt;</span></span> <span class="parameter-name">elements</span></span>)
    </section>

    <section class="desc markdown">
      <p>Creates a double-linked queue from <code>elements</code>.</p>
<p>The element order in the queue is as if the elements were added using
<a href="dart-collection/DoubleLinkedQueue/addLast.html">addLast</a> in the order provided by <code>elements</code>.iterator.</p>
    </section>
    
    <section class="summary source-code" id="source">
      <h2><span>Implementation</span></h2>
      <pre class="language-dart"><code class="language-dart">factory DoubleLinkedQueue.of(Iterable&lt;E&gt; elements) =&gt;
    DoubleLinkedQueue&lt;E&gt;()..addAll(elements);</code></pre>
    </section>

  </div> <!-- /.main-content -->

  <div id="dartdoc-sidebar-right" class="col-xs-6 col-sm-6 col-md-2 sidebar sidebar-offcanvas-right">
  </div><!--/.sidebar-offcanvas-->

</main>

<footer>
  <span class="no-break">
    gms_check
      1.0.0
  </span>

  <!-- footer-text placeholder -->
</footer>

<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.2.1/jquery.min.js"></script>
<script src="static-assets/typeahead.bundle.min.js"></script>
<script src="static-assets/highlight.pack.js"></script>
<script src="static-assets/URI.js"></script>
<script src="static-assets/script.js"></script>
<!-- footer placeholder -->

</body>

</html>
