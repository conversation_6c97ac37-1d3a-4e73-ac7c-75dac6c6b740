<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, height=device-height, initial-scale=1, user-scalable=no">
  <meta name="description" content="API docs for the ListQueue.of constructor from the Class ListQueue class from the dart:collection library, for the Dart programming language.">
  <title>ListQueue.of constructor - ListQueue class - dart:collection library - Dart API</title>
  <!-- required because all the links are pseudo-absolute -->
  <base href="../..">

  <link href="https://fonts.googleapis.com/css?family=Source+Code+Pro:500,400i,400,300|Source+Sans+Pro:400,300,700" rel="stylesheet">
  <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
  <link rel="stylesheet" href="static-assets/github.css">
  <link rel="stylesheet" href="static-assets/styles.css">
  <link rel="icon" href="static-assets/favicon.png">
  <!-- header placeholder -->
</head>

<body>

<div id="overlay-under-drawer"></div>

<header id="title">
  <button id="sidenav-left-toggle" type="button">&nbsp;</button>
  <ol class="breadcrumbs gt-separated dark hidden-xs">
    <li><a href="index.html">gms_check</a></li>
    <li><a href="dart-collection/dart-collection-library.html">dart:collection</a></li>
    <li><a href="dart-collection/ListQueue-class.html">ListQueue<span class="signature">&lt;<wbr><span class="type-parameter">E</span>&gt;</span></a></li>
    <li class="self-crumb">ListQueue.of factory constructor</li>
  </ol>
  <div class="self-name">ListQueue.of</div>
  <form class="search navbar-right" role="search">
    <input type="text" id="search-box" autocomplete="off" disabled class="form-control typeahead" placeholder="Loading search...">
  </form>
</header>

<main>

  <div id="dartdoc-sidebar-left" class="col-xs-6 col-sm-3 col-md-2 sidebar sidebar-offcanvas-left">
    <header id="header-search-sidebar" class="hidden-l">
      <form class="search-sidebar" role="search">
        <input type="text" id="search-sidebar" autocomplete="off" disabled class="form-control typeahead" placeholder="Loading search...">
      </form>
    </header>
    
    <ol class="breadcrumbs gt-separated dark hidden-l" id="sidebar-nav">
      <li><a href="index.html">gms_check</a></li>
      <li><a href="dart-collection/dart-collection-library.html">dart:collection</a></li>
      <li><a href="dart-collection/ListQueue-class.html">ListQueue<span class="signature">&lt;<wbr><span class="type-parameter">E</span>&gt;</span></a></li>
      <li class="self-crumb">ListQueue.of factory constructor</li>
    </ol>
    
    <h5>ListQueue class</h5>
    <ol>
    
      <li class="section-title"><a href="dart-collection/ListQueue-class.html#constructors">Constructors</a></li>
      <li><a href="dart-collection/ListQueue/ListQueue.html">ListQueue</a></li>
      <li><a href="dart-collection/ListQueue/ListQueue.from.html">from</a></li>
      <li><a href="dart-collection/ListQueue/ListQueue.of.html">of</a></li>
    
      <li class="section-title">
        <a href="dart-collection/ListQueue-class.html#instance-properties">Properties</a>
      </li>
      <li><a href="dart-collection/ListQueue/first.html">first</a></li>
      <li><a href="dart-collection/ListQueue/isEmpty.html">isEmpty</a></li>
      <li><a href="dart-collection/ListQueue/iterator.html">iterator</a></li>
      <li><a href="dart-collection/ListQueue/last.html">last</a></li>
      <li><a href="dart-collection/ListQueue/length.html">length</a></li>
      <li><a href="dart-collection/ListQueue/single.html">single</a></li>
      <li class="inherited"><a href="dart-core/Object/hashCode.html">hashCode</a></li>
      <li class="inherited"><a href="dart-core/Iterable/isNotEmpty.html">isNotEmpty</a></li>
      <li class="inherited"><a href="dart-core/Object/runtimeType.html">runtimeType</a></li>
    
      <li class="section-title"><a href="dart-collection/ListQueue-class.html#instance-methods">Methods</a></li>
      <li><a href="dart-collection/ListQueue/add.html">add</a></li>
      <li><a href="dart-collection/ListQueue/addAll.html">addAll</a></li>
      <li><a href="dart-collection/ListQueue/addFirst.html">addFirst</a></li>
      <li><a href="dart-collection/ListQueue/addLast.html">addLast</a></li>
      <li><a href="dart-collection/ListQueue/cast.html">cast</a></li>
      <li><a href="dart-collection/ListQueue/clear.html">clear</a></li>
      <li><a href="dart-collection/ListQueue/elementAt.html">elementAt</a></li>
      <li><a href="dart-collection/ListQueue/forEach.html">forEach</a></li>
      <li><a href="dart-collection/ListQueue/remove.html">remove</a></li>
      <li><a href="dart-collection/ListQueue/removeFirst.html">removeFirst</a></li>
      <li><a href="dart-collection/ListQueue/removeLast.html">removeLast</a></li>
      <li><a href="dart-collection/ListQueue/removeWhere.html">removeWhere</a></li>
      <li><a href="dart-collection/ListQueue/retainWhere.html">retainWhere</a></li>
      <li><a href="dart-collection/ListQueue/toList.html">toList</a></li>
      <li><a href="dart-collection/ListQueue/toString.html">toString</a></li>
      <li class="inherited"><a href="dart-collection/ListQueue/any.html">any</a></li>
      <li class="inherited"><a href="dart-collection/ListQueue/contains.html">contains</a></li>
      <li class="inherited"><a href="dart-collection/ListQueue/every.html">every</a></li>
      <li class="inherited"><a href="dart-core/Iterable/expand.html">expand</a></li>
      <li class="inherited"><a href="dart-collection/ListQueue/firstWhere.html">firstWhere</a></li>
      <li class="inherited"><a href="dart-collection/ListQueue/fold.html">fold</a></li>
      <li class="inherited"><a href="dart-core/Iterable/followedBy.html">followedBy</a></li>
      <li class="inherited"><a href="dart-collection/ListQueue/join.html">join</a></li>
      <li class="inherited"><a href="dart-collection/ListQueue/lastWhere.html">lastWhere</a></li>
      <li class="inherited"><a href="dart-collection/ListQueue/map.html">map</a></li>
      <li class="inherited"><a href="dart-core/Object/noSuchMethod.html">noSuchMethod</a></li>
      <li class="inherited"><a href="dart-collection/ListQueue/reduce.html">reduce</a></li>
      <li class="inherited"><a href="dart-collection/ListQueue/singleWhere.html">singleWhere</a></li>
      <li class="inherited"><a href="dart-collection/ListQueue/skip.html">skip</a></li>
      <li class="inherited"><a href="dart-collection/ListQueue/skipWhile.html">skipWhile</a></li>
      <li class="inherited"><a href="dart-collection/ListQueue/take.html">take</a></li>
      <li class="inherited"><a href="dart-collection/ListQueue/takeWhile.html">takeWhile</a></li>
      <li class="inherited"><a href="dart-collection/ListQueue/toSet.html">toSet</a></li>
      <li class="inherited"><a href="dart-collection/ListQueue/where.html">where</a></li>
      <li class="inherited"><a href="dart-core/Iterable/whereType.html">whereType</a></li>
    
      <li class="section-title inherited"><a href="dart-collection/ListQueue-class.html#operators">Operators</a></li>
      <li class="inherited"><a href="dart-core/Object/operator_equals.html">operator ==</a></li>
    
    
    
    
    </ol>
  </div><!--/.sidebar-offcanvas-left-->

  <div id="dartdoc-main-content" class="col-xs-12 col-sm-9 col-md-8 main-content">
      <div><h1><span class="kind-constructor">ListQueue&lt;<wbr><span class="type-parameter">E</span>&gt;.of</span> constructor</h1></div>

    <section class="multi-line-signature">
      
      <span class="name ">ListQueue&lt;<wbr><span class="type-parameter">E</span>&gt;.of</span>(<wbr><span class="parameter" id="of-param-elements"><span class="type-annotation"><a href="dart-core/Iterable-class.html">Iterable</a><span class="signature">&lt;<wbr><span class="type-parameter">E</span>&gt;</span></span> <span class="parameter-name">elements</span></span>)
    </section>

    <section class="desc markdown">
      <p>Create a <code>ListQueue</code> from <code>elements</code>.</p>
<p>The elements are added to the queue, as by <a href="dart-collection/ListQueue/addLast.html">addLast</a>, in the order given
by <code>elements.iterator</code>.</p>
    </section>
    
    <section class="summary source-code" id="source">
      <h2><span>Implementation</span></h2>
      <pre class="language-dart"><code class="language-dart">factory ListQueue.of(Iterable&lt;E&gt; elements) =&gt;
    ListQueue&lt;E&gt;()..addAll(elements);</code></pre>
    </section>

  </div> <!-- /.main-content -->

  <div id="dartdoc-sidebar-right" class="col-xs-6 col-sm-6 col-md-2 sidebar sidebar-offcanvas-right">
  </div><!--/.sidebar-offcanvas-->

</main>

<footer>
  <span class="no-break">
    gms_check
      1.0.0
  </span>

  <!-- footer-text placeholder -->
</footer>

<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.2.1/jquery.min.js"></script>
<script src="static-assets/typeahead.bundle.min.js"></script>
<script src="static-assets/highlight.pack.js"></script>
<script src="static-assets/URI.js"></script>
<script src="static-assets/script.js"></script>
<!-- footer placeholder -->

</body>

</html>
