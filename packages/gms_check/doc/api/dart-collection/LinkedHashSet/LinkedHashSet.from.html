<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, height=device-height, initial-scale=1, user-scalable=no">
  <meta name="description" content="API docs for the LinkedHashSet.from constructor from the Class LinkedHashSet class from the dart:collection library, for the Dart programming language.">
  <title>LinkedHashSet.from constructor - LinkedHashSet class - dart:collection library - Dart API</title>
  <!-- required because all the links are pseudo-absolute -->
  <base href="../..">

  <link href="https://fonts.googleapis.com/css?family=Source+Code+Pro:500,400i,400,300|Source+Sans+Pro:400,300,700" rel="stylesheet">
  <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
  <link rel="stylesheet" href="static-assets/github.css">
  <link rel="stylesheet" href="static-assets/styles.css">
  <link rel="icon" href="static-assets/favicon.png">
  <!-- header placeholder -->
</head>

<body>

<div id="overlay-under-drawer"></div>

<header id="title">
  <button id="sidenav-left-toggle" type="button">&nbsp;</button>
  <ol class="breadcrumbs gt-separated dark hidden-xs">
    <li><a href="index.html">gms_check</a></li>
    <li><a href="dart-collection/dart-collection-library.html">dart:collection</a></li>
    <li><a href="dart-collection/LinkedHashSet-class.html">LinkedHashSet<span class="signature">&lt;<wbr><span class="type-parameter">E</span>&gt;</span></a></li>
    <li class="self-crumb">LinkedHashSet.from factory constructor</li>
  </ol>
  <div class="self-name">LinkedHashSet.from</div>
  <form class="search navbar-right" role="search">
    <input type="text" id="search-box" autocomplete="off" disabled class="form-control typeahead" placeholder="Loading search...">
  </form>
</header>

<main>

  <div id="dartdoc-sidebar-left" class="col-xs-6 col-sm-3 col-md-2 sidebar sidebar-offcanvas-left">
    <header id="header-search-sidebar" class="hidden-l">
      <form class="search-sidebar" role="search">
        <input type="text" id="search-sidebar" autocomplete="off" disabled class="form-control typeahead" placeholder="Loading search...">
      </form>
    </header>
    
    <ol class="breadcrumbs gt-separated dark hidden-l" id="sidebar-nav">
      <li><a href="index.html">gms_check</a></li>
      <li><a href="dart-collection/dart-collection-library.html">dart:collection</a></li>
      <li><a href="dart-collection/LinkedHashSet-class.html">LinkedHashSet<span class="signature">&lt;<wbr><span class="type-parameter">E</span>&gt;</span></a></li>
      <li class="self-crumb">LinkedHashSet.from factory constructor</li>
    </ol>
    
    <h5>LinkedHashSet class</h5>
    <ol>
    
      <li class="section-title"><a href="dart-collection/LinkedHashSet-class.html#constructors">Constructors</a></li>
      <li><a href="dart-collection/LinkedHashSet/LinkedHashSet.html">LinkedHashSet</a></li>
      <li><a href="dart-collection/LinkedHashSet/LinkedHashSet.from.html">from</a></li>
      <li><a href="dart-collection/LinkedHashSet/LinkedHashSet.identity.html">identity</a></li>
      <li><a href="dart-collection/LinkedHashSet/LinkedHashSet.of.html">of</a></li>
    
      <li class="section-title">
        <a href="dart-collection/LinkedHashSet-class.html#instance-properties">Properties</a>
      </li>
      <li><a href="dart-collection/LinkedHashSet/iterator.html">iterator</a></li>
      <li class="inherited"><a href="dart-core/Iterable/first.html">first</a></li>
      <li class="inherited"><a href="dart-core/Object/hashCode.html">hashCode</a></li>
      <li class="inherited"><a href="dart-core/Iterable/isEmpty.html">isEmpty</a></li>
      <li class="inherited"><a href="dart-core/Iterable/isNotEmpty.html">isNotEmpty</a></li>
      <li class="inherited"><a href="dart-core/Iterable/last.html">last</a></li>
      <li class="inherited"><a href="dart-collection/LinkedHashSet/length.html">length</a></li>
      <li class="inherited"><a href="dart-core/Object/runtimeType.html">runtimeType</a></li>
      <li class="inherited"><a href="dart-core/Iterable/single.html">single</a></li>
    
      <li class="section-title"><a href="dart-collection/LinkedHashSet-class.html#instance-methods">Methods</a></li>
      <li><a href="dart-collection/LinkedHashSet/forEach.html">forEach</a></li>
      <li class="inherited"><a href="dart-core/Set/add.html">add</a></li>
      <li class="inherited"><a href="dart-core/Set/addAll.html">addAll</a></li>
      <li class="inherited"><a href="dart-core/Iterable/any.html">any</a></li>
      <li class="inherited"><a href="dart-core/Set/cast.html">cast</a></li>
      <li class="inherited"><a href="dart-core/Set/clear.html">clear</a></li>
      <li class="inherited"><a href="dart-core/Set/contains.html">contains</a></li>
      <li class="inherited"><a href="dart-core/Set/containsAll.html">containsAll</a></li>
      <li class="inherited"><a href="dart-core/Set/difference.html">difference</a></li>
      <li class="inherited"><a href="dart-core/Iterable/elementAt.html">elementAt</a></li>
      <li class="inherited"><a href="dart-core/Iterable/every.html">every</a></li>
      <li class="inherited"><a href="dart-core/Iterable/expand.html">expand</a></li>
      <li class="inherited"><a href="dart-core/Iterable/firstWhere.html">firstWhere</a></li>
      <li class="inherited"><a href="dart-core/Iterable/fold.html">fold</a></li>
      <li class="inherited"><a href="dart-core/Iterable/followedBy.html">followedBy</a></li>
      <li class="inherited"><a href="dart-core/Set/intersection.html">intersection</a></li>
      <li class="inherited"><a href="dart-core/Iterable/join.html">join</a></li>
      <li class="inherited"><a href="dart-core/Iterable/lastWhere.html">lastWhere</a></li>
      <li class="inherited"><a href="dart-core/Set/lookup.html">lookup</a></li>
      <li class="inherited"><a href="dart-core/Iterable/map.html">map</a></li>
      <li class="inherited"><a href="dart-core/Object/noSuchMethod.html">noSuchMethod</a></li>
      <li class="inherited"><a href="dart-core/Iterable/reduce.html">reduce</a></li>
      <li class="inherited"><a href="dart-core/Set/remove.html">remove</a></li>
      <li class="inherited"><a href="dart-core/Set/removeAll.html">removeAll</a></li>
      <li class="inherited"><a href="dart-core/Set/removeWhere.html">removeWhere</a></li>
      <li class="inherited"><a href="dart-core/Set/retainAll.html">retainAll</a></li>
      <li class="inherited"><a href="dart-core/Set/retainWhere.html">retainWhere</a></li>
      <li class="inherited"><a href="dart-core/Iterable/singleWhere.html">singleWhere</a></li>
      <li class="inherited"><a href="dart-core/Iterable/skip.html">skip</a></li>
      <li class="inherited"><a href="dart-core/Iterable/skipWhile.html">skipWhile</a></li>
      <li class="inherited"><a href="dart-core/Iterable/take.html">take</a></li>
      <li class="inherited"><a href="dart-core/Iterable/takeWhile.html">takeWhile</a></li>
      <li class="inherited"><a href="dart-core/Iterable/toList.html">toList</a></li>
      <li class="inherited"><a href="dart-core/Set/toSet.html">toSet</a></li>
      <li class="inherited"><a href="dart-core/Object/toString.html">toString</a></li>
      <li class="inherited"><a href="dart-core/Set/union.html">union</a></li>
      <li class="inherited"><a href="dart-core/Iterable/where.html">where</a></li>
      <li class="inherited"><a href="dart-core/Iterable/whereType.html">whereType</a></li>
    
      <li class="section-title inherited"><a href="dart-collection/LinkedHashSet-class.html#operators">Operators</a></li>
      <li class="inherited"><a href="dart-core/Object/operator_equals.html">operator ==</a></li>
    
    
    
    
    </ol>
  </div><!--/.sidebar-offcanvas-left-->

  <div id="dartdoc-main-content" class="col-xs-12 col-sm-9 col-md-8 main-content">
      <div><h1><span class="kind-constructor">LinkedHashSet&lt;<wbr><span class="type-parameter">E</span>&gt;.from</span> constructor</h1></div>

    <section class="multi-line-signature">
      
      <span class="name ">LinkedHashSet&lt;<wbr><span class="type-parameter">E</span>&gt;.from</span>(<wbr><span class="parameter" id="from-param-elements"><span class="type-annotation"><a href="dart-core/Iterable-class.html">Iterable</a></span> <span class="parameter-name">elements</span></span>)
    </section>

    <section class="desc markdown">
      <p>Create a linked hash set containing all <code>elements</code>.</p>
<p>Creates a linked hash set as by <code>new LinkedHashSet&lt;E&gt;()</code> and adds each
element of <code>elements</code> to this set in the order they are iterated.</p>
<p>All the <code>elements</code> should be instances of <code>E</code>.
The <code>elements</code> iterable itself may have any element type,
so this constructor can be used to down-cast a <code>Set</code>, for example as:</p>
<pre class="language-dart"><code class="language-dart">Set&lt;SuperType&gt; superSet = ...;
Iterable&lt;SuperType&gt; tmp = superSet.where((e) =&gt; e is SubType);
Set&lt;SubType&gt; subSet = LinkedHashSet&lt;SubType&gt;.from(tmp);
</code></pre>
    </section>
    
    <section class="summary source-code" id="source">
      <h2><span>Implementation</span></h2>
      <pre class="language-dart"><code class="language-dart">factory LinkedHashSet.from(Iterable&lt;dynamic&gt; elements) {
  LinkedHashSet&lt;E&gt; result = LinkedHashSet&lt;E&gt;();
  for (final element in elements) {
    result.add(element as E);
  }
  return result;
}</code></pre>
    </section>

  </div> <!-- /.main-content -->

  <div id="dartdoc-sidebar-right" class="col-xs-6 col-sm-6 col-md-2 sidebar sidebar-offcanvas-right">
  </div><!--/.sidebar-offcanvas-->

</main>

<footer>
  <span class="no-break">
    gms_check
      1.0.0
  </span>

  <!-- footer-text placeholder -->
</footer>

<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.2.1/jquery.min.js"></script>
<script src="static-assets/typeahead.bundle.min.js"></script>
<script src="static-assets/highlight.pack.js"></script>
<script src="static-assets/URI.js"></script>
<script src="static-assets/script.js"></script>
<!-- footer placeholder -->

</body>

</html>
