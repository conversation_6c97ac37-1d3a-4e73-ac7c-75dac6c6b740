<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, height=device-height, initial-scale=1, user-scalable=no">
  <meta name="description" content="API docs for the LinkedHashSet constructor from the Class LinkedHashSet class from the dart:collection library, for the Dart programming language.">
  <title>LinkedHashSet constructor - LinkedHashSet class - dart:collection library - Dart API</title>
  <!-- required because all the links are pseudo-absolute -->
  <base href="../..">

  <link href="https://fonts.googleapis.com/css?family=Source+Code+Pro:500,400i,400,300|Source+Sans+Pro:400,300,700" rel="stylesheet">
  <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
  <link rel="stylesheet" href="static-assets/github.css">
  <link rel="stylesheet" href="static-assets/styles.css">
  <link rel="icon" href="static-assets/favicon.png">
  <!-- header placeholder -->
</head>

<body>

<div id="overlay-under-drawer"></div>

<header id="title">
  <button id="sidenav-left-toggle" type="button">&nbsp;</button>
  <ol class="breadcrumbs gt-separated dark hidden-xs">
    <li><a href="index.html">gms_check</a></li>
    <li><a href="dart-collection/dart-collection-library.html">dart:collection</a></li>
    <li><a href="dart-collection/LinkedHashSet-class.html">LinkedHashSet<span class="signature">&lt;<wbr><span class="type-parameter">E</span>&gt;</span></a></li>
    <li class="self-crumb">LinkedHashSet factory constructor</li>
  </ol>
  <div class="self-name">LinkedHashSet</div>
  <form class="search navbar-right" role="search">
    <input type="text" id="search-box" autocomplete="off" disabled class="form-control typeahead" placeholder="Loading search...">
  </form>
</header>

<main>

  <div id="dartdoc-sidebar-left" class="col-xs-6 col-sm-3 col-md-2 sidebar sidebar-offcanvas-left">
    <header id="header-search-sidebar" class="hidden-l">
      <form class="search-sidebar" role="search">
        <input type="text" id="search-sidebar" autocomplete="off" disabled class="form-control typeahead" placeholder="Loading search...">
      </form>
    </header>
    
    <ol class="breadcrumbs gt-separated dark hidden-l" id="sidebar-nav">
      <li><a href="index.html">gms_check</a></li>
      <li><a href="dart-collection/dart-collection-library.html">dart:collection</a></li>
      <li><a href="dart-collection/LinkedHashSet-class.html">LinkedHashSet<span class="signature">&lt;<wbr><span class="type-parameter">E</span>&gt;</span></a></li>
      <li class="self-crumb">LinkedHashSet factory constructor</li>
    </ol>
    
    <h5>LinkedHashSet class</h5>
    <ol>
    
      <li class="section-title"><a href="dart-collection/LinkedHashSet-class.html#constructors">Constructors</a></li>
      <li><a href="dart-collection/LinkedHashSet/LinkedHashSet.html">LinkedHashSet</a></li>
      <li><a href="dart-collection/LinkedHashSet/LinkedHashSet.from.html">from</a></li>
      <li><a href="dart-collection/LinkedHashSet/LinkedHashSet.identity.html">identity</a></li>
      <li><a href="dart-collection/LinkedHashSet/LinkedHashSet.of.html">of</a></li>
    
      <li class="section-title">
        <a href="dart-collection/LinkedHashSet-class.html#instance-properties">Properties</a>
      </li>
      <li><a href="dart-collection/LinkedHashSet/iterator.html">iterator</a></li>
      <li class="inherited"><a href="dart-core/Iterable/first.html">first</a></li>
      <li class="inherited"><a href="dart-core/Object/hashCode.html">hashCode</a></li>
      <li class="inherited"><a href="dart-core/Iterable/isEmpty.html">isEmpty</a></li>
      <li class="inherited"><a href="dart-core/Iterable/isNotEmpty.html">isNotEmpty</a></li>
      <li class="inherited"><a href="dart-core/Iterable/last.html">last</a></li>
      <li class="inherited"><a href="dart-collection/LinkedHashSet/length.html">length</a></li>
      <li class="inherited"><a href="dart-core/Object/runtimeType.html">runtimeType</a></li>
      <li class="inherited"><a href="dart-core/Iterable/single.html">single</a></li>
    
      <li class="section-title"><a href="dart-collection/LinkedHashSet-class.html#instance-methods">Methods</a></li>
      <li><a href="dart-collection/LinkedHashSet/forEach.html">forEach</a></li>
      <li class="inherited"><a href="dart-core/Set/add.html">add</a></li>
      <li class="inherited"><a href="dart-core/Set/addAll.html">addAll</a></li>
      <li class="inherited"><a href="dart-core/Iterable/any.html">any</a></li>
      <li class="inherited"><a href="dart-core/Set/cast.html">cast</a></li>
      <li class="inherited"><a href="dart-core/Set/clear.html">clear</a></li>
      <li class="inherited"><a href="dart-core/Set/contains.html">contains</a></li>
      <li class="inherited"><a href="dart-core/Set/containsAll.html">containsAll</a></li>
      <li class="inherited"><a href="dart-core/Set/difference.html">difference</a></li>
      <li class="inherited"><a href="dart-core/Iterable/elementAt.html">elementAt</a></li>
      <li class="inherited"><a href="dart-core/Iterable/every.html">every</a></li>
      <li class="inherited"><a href="dart-core/Iterable/expand.html">expand</a></li>
      <li class="inherited"><a href="dart-core/Iterable/firstWhere.html">firstWhere</a></li>
      <li class="inherited"><a href="dart-core/Iterable/fold.html">fold</a></li>
      <li class="inherited"><a href="dart-core/Iterable/followedBy.html">followedBy</a></li>
      <li class="inherited"><a href="dart-core/Set/intersection.html">intersection</a></li>
      <li class="inherited"><a href="dart-core/Iterable/join.html">join</a></li>
      <li class="inherited"><a href="dart-core/Iterable/lastWhere.html">lastWhere</a></li>
      <li class="inherited"><a href="dart-core/Set/lookup.html">lookup</a></li>
      <li class="inherited"><a href="dart-core/Iterable/map.html">map</a></li>
      <li class="inherited"><a href="dart-core/Object/noSuchMethod.html">noSuchMethod</a></li>
      <li class="inherited"><a href="dart-core/Iterable/reduce.html">reduce</a></li>
      <li class="inherited"><a href="dart-core/Set/remove.html">remove</a></li>
      <li class="inherited"><a href="dart-core/Set/removeAll.html">removeAll</a></li>
      <li class="inherited"><a href="dart-core/Set/removeWhere.html">removeWhere</a></li>
      <li class="inherited"><a href="dart-core/Set/retainAll.html">retainAll</a></li>
      <li class="inherited"><a href="dart-core/Set/retainWhere.html">retainWhere</a></li>
      <li class="inherited"><a href="dart-core/Iterable/singleWhere.html">singleWhere</a></li>
      <li class="inherited"><a href="dart-core/Iterable/skip.html">skip</a></li>
      <li class="inherited"><a href="dart-core/Iterable/skipWhile.html">skipWhile</a></li>
      <li class="inherited"><a href="dart-core/Iterable/take.html">take</a></li>
      <li class="inherited"><a href="dart-core/Iterable/takeWhile.html">takeWhile</a></li>
      <li class="inherited"><a href="dart-core/Iterable/toList.html">toList</a></li>
      <li class="inherited"><a href="dart-core/Set/toSet.html">toSet</a></li>
      <li class="inherited"><a href="dart-core/Object/toString.html">toString</a></li>
      <li class="inherited"><a href="dart-core/Set/union.html">union</a></li>
      <li class="inherited"><a href="dart-core/Iterable/where.html">where</a></li>
      <li class="inherited"><a href="dart-core/Iterable/whereType.html">whereType</a></li>
    
      <li class="section-title inherited"><a href="dart-collection/LinkedHashSet-class.html#operators">Operators</a></li>
      <li class="inherited"><a href="dart-core/Object/operator_equals.html">operator ==</a></li>
    
    
    
    
    </ol>
  </div><!--/.sidebar-offcanvas-left-->

  <div id="dartdoc-main-content" class="col-xs-12 col-sm-9 col-md-8 main-content">
      <div><h1><span class="kind-constructor">LinkedHashSet&lt;<wbr><span class="type-parameter">E</span>&gt;</span> constructor</h1></div>

    <section class="multi-line-signature">
      
      <span class="name ">LinkedHashSet&lt;<wbr><span class="type-parameter">E</span>&gt;</span>(<wbr>{<span class="parameter" id="-param-equals"><span class="type-annotation"><a href="dart-core/bool-class.html">bool</a></span> <span class="parameter-name">equals</span>(<span class="parameter" id="param-"><span class="type-annotation">E</span></span> <span class="parameter" id="param-"><span class="type-annotation">E</span></span>), </span> <span class="parameter" id="-param-hashCode"><span class="type-annotation"><a href="dart-core/int-class.html">int</a></span> <span class="parameter-name">hashCode</span>(<span class="parameter" id="param-"><span class="type-annotation">E</span></span>), </span> <span class="parameter" id="-param-isValidKey"><span class="type-annotation"><a href="dart-core/bool-class.html">bool</a></span> <span class="parameter-name">isValidKey</span>(<span class="parameter" id="param-"><span class="type-annotation">dynamic</span></span>)</span> })
    </section>

    <section class="desc markdown">
      <p>Create an insertion-ordered hash set using the provided
<code>equals</code> and <code>hashCode</code>.</p>
<p>The provided <code>equals</code> must define a stable equivalence relation, and
<code>hashCode</code> must be consistent with <code>equals</code>. If the <code>equals</code> or <code>hashCode</code>
methods won't work on all objects, but only on some instances of E, the
<code>isValidKey</code> predicate can be used to restrict the keys that the functions
are applied to.
Any key for which <code>isValidKey</code> returns false is automatically assumed
to not be in the set when asking <code>contains</code>.</p>
<p>If <code>equals</code> or <code>hashCode</code> are omitted, the set uses
the elements' intrinsic <a href="dart-core/Object/operator_equals.html">Object.==</a> and <a href="dart-core/Object/hashCode.html">Object.hashCode</a>,
and <code>isValidKey</code> is ignored since these operations are assumed
to work on all objects.</p>
<p>If you supply one of <code>equals</code> and <code>hashCode</code>,
you should generally also to supply the other.</p>
<p>If the supplied <code>equals</code> or <code>hashCode</code> functions won't work on all <code>E</code>
objects, and the map will be used in a setting where a non-<code>E</code> object
is passed to, e.g., <code>contains</code>, then the <code>isValidKey</code> function should
also be supplied.</p>
<p>If <code>isValidKey</code> is omitted, it defaults to testing if the object is an
<code>E</code> instance. That means that:</p>
<pre class="language-dart"><code class="language-dart">LinkedHashSet&lt;int&gt;(equals: (int e1, int e2) =&gt; (e1 - e2) % 5 == 0,
                   hashCode: (int e) =&gt; e % 5)
</code></pre>
<p>does not need an <code>isValidKey</code> argument, because it defaults to only
accepting <code>int</code> values which are accepted by both <code>equals</code> and <code>hashCode</code>.</p>
<p>If neither <code>equals</code>, <code>hashCode</code>, nor <code>isValidKey</code> is provided,
the default <code>isValidKey</code> instead accepts all values.
The default equality and hashcode operations are assumed to work on all
objects.</p>
<p>Likewise, if <code>equals</code> is <a href="dart-core/identical.html">identical</a>, <code>hashCode</code> is <a href="dart-core/identityHashCode.html">identityHashCode</a>
and <code>isValidKey</code> is omitted, the resulting set is identity based,
and the <code>isValidKey</code> defaults to accepting all keys.
Such a map can be created directly using <a href="dart-collection/LinkedHashSet/LinkedHashSet.identity.html">LinkedHashSet.identity</a>.</p>
    </section>
    
    <section class="summary source-code" id="source">
      <h2><span>Implementation</span></h2>
      <pre class="language-dart"><code class="language-dart">external factory LinkedHashSet(
    {bool Function(E, E)? equals,
    int Function(E)? hashCode,
    bool Function(dynamic)? isValidKey});</code></pre>
    </section>

  </div> <!-- /.main-content -->

  <div id="dartdoc-sidebar-right" class="col-xs-6 col-sm-6 col-md-2 sidebar sidebar-offcanvas-right">
  </div><!--/.sidebar-offcanvas-->

</main>

<footer>
  <span class="no-break">
    gms_check
      1.0.0
  </span>

  <!-- footer-text placeholder -->
</footer>

<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.2.1/jquery.min.js"></script>
<script src="static-assets/typeahead.bundle.min.js"></script>
<script src="static-assets/highlight.pack.js"></script>
<script src="static-assets/URI.js"></script>
<script src="static-assets/script.js"></script>
<!-- footer placeholder -->

</body>

</html>
