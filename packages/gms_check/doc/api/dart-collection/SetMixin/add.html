<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, height=device-height, initial-scale=1, user-scalable=no">
  <meta name="description" content="API docs for the add method from the SetMixin class, for the Dart programming language.">
  <title>add method - SetMixin class - dart:collection library - Dart API</title>
  <!-- required because all the links are pseudo-absolute -->
  <base href="../..">

  <link href="https://fonts.googleapis.com/css?family=Source+Code+Pro:500,400i,400,300|Source+Sans+Pro:400,300,700" rel="stylesheet">
  <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
  <link rel="stylesheet" href="static-assets/github.css">
  <link rel="stylesheet" href="static-assets/styles.css">
  <link rel="icon" href="static-assets/favicon.png">
  <!-- header placeholder -->
</head>

<body>

<div id="overlay-under-drawer"></div>

<header id="title">
  <button id="sidenav-left-toggle" type="button">&nbsp;</button>
  <ol class="breadcrumbs gt-separated dark hidden-xs">
    <li><a href="index.html">gms_check</a></li>
    <li><a href="dart-collection/dart-collection-library.html">dart:collection</a></li>
    <li><a href="dart-collection/SetMixin-class.html">SetMixin<span class="signature">&lt;<wbr><span class="type-parameter">E</span>&gt;</span></a></li>
    <li class="self-crumb">add abstract method</li>
  </ol>
  <div class="self-name">add</div>
  <form class="search navbar-right" role="search">
    <input type="text" id="search-box" autocomplete="off" disabled class="form-control typeahead" placeholder="Loading search...">
  </form>
</header>

<main>

  <div id="dartdoc-sidebar-left" class="col-xs-6 col-sm-3 col-md-2 sidebar sidebar-offcanvas-left">
    <header id="header-search-sidebar" class="hidden-l">
      <form class="search-sidebar" role="search">
        <input type="text" id="search-sidebar" autocomplete="off" disabled class="form-control typeahead" placeholder="Loading search...">
      </form>
    </header>
    
    <ol class="breadcrumbs gt-separated dark hidden-l" id="sidebar-nav">
      <li><a href="index.html">gms_check</a></li>
      <li><a href="dart-collection/dart-collection-library.html">dart:collection</a></li>
      <li><a href="dart-collection/SetMixin-class.html">SetMixin<span class="signature">&lt;<wbr><span class="type-parameter">E</span>&gt;</span></a></li>
      <li class="self-crumb">add abstract method</li>
    </ol>
    
    <h5>SetMixin class</h5>
    <ol>
    
        <li class="section-title"><a href="dart-collection/SetMixin-class.html#constructors">Constructors</a></li>
        <li><a href="dart-collection/SetMixin/SetMixin.html">SetMixin</a></li>
    
        <li class="section-title">
            <a href="dart-collection/SetMixin-class.html#instance-properties">Properties</a>
        </li>
        <li><a href="dart-collection/SetMixin/first.html">first</a></li>
        <li><a href="dart-collection/SetMixin/isEmpty.html">isEmpty</a></li>
        <li><a href="dart-collection/SetMixin/isNotEmpty.html">isNotEmpty</a></li>
        <li><a href="dart-collection/SetMixin/iterator.html">iterator</a></li>
        <li><a href="dart-collection/SetMixin/last.html">last</a></li>
        <li><a href="dart-collection/SetMixin/length.html">length</a></li>
        <li><a href="dart-collection/SetMixin/single.html">single</a></li>
        <li class="inherited"><a href="dart-core/Object/hashCode.html">hashCode</a></li>
        <li class="inherited"><a href="dart-core/Object/runtimeType.html">runtimeType</a></li>
    
        <li class="section-title"><a href="dart-collection/SetMixin-class.html#instance-methods">Methods</a></li>
        <li><a href="dart-collection/SetMixin/add.html">add</a></li>
        <li><a href="dart-collection/SetMixin/addAll.html">addAll</a></li>
        <li><a href="dart-collection/SetMixin/any.html">any</a></li>
        <li><a href="dart-collection/SetMixin/cast.html">cast</a></li>
        <li><a href="dart-collection/SetMixin/clear.html">clear</a></li>
        <li><a href="dart-collection/SetMixin/contains.html">contains</a></li>
        <li><a href="dart-collection/SetMixin/containsAll.html">containsAll</a></li>
        <li><a href="dart-collection/SetMixin/difference.html">difference</a></li>
        <li><a href="dart-collection/SetMixin/elementAt.html">elementAt</a></li>
        <li><a href="dart-collection/SetMixin/every.html">every</a></li>
        <li><a href="dart-collection/SetMixin/expand.html">expand</a></li>
        <li><a href="dart-collection/SetMixin/firstWhere.html">firstWhere</a></li>
        <li><a href="dart-collection/SetMixin/fold.html">fold</a></li>
        <li><a href="dart-collection/SetMixin/followedBy.html">followedBy</a></li>
        <li><a href="dart-collection/SetMixin/forEach.html">forEach</a></li>
        <li><a href="dart-collection/SetMixin/intersection.html">intersection</a></li>
        <li><a href="dart-collection/SetMixin/join.html">join</a></li>
        <li><a href="dart-collection/SetMixin/lastWhere.html">lastWhere</a></li>
        <li><a href="dart-collection/SetMixin/lookup.html">lookup</a></li>
        <li><a href="dart-collection/SetMixin/map.html">map</a></li>
        <li><a href="dart-collection/SetMixin/reduce.html">reduce</a></li>
        <li><a href="dart-collection/SetMixin/remove.html">remove</a></li>
        <li><a href="dart-collection/SetMixin/removeAll.html">removeAll</a></li>
        <li><a href="dart-collection/SetMixin/removeWhere.html">removeWhere</a></li>
        <li><a href="dart-collection/SetMixin/retainAll.html">retainAll</a></li>
        <li><a href="dart-collection/SetMixin/retainWhere.html">retainWhere</a></li>
        <li><a href="dart-collection/SetMixin/singleWhere.html">singleWhere</a></li>
        <li><a href="dart-collection/SetMixin/skip.html">skip</a></li>
        <li><a href="dart-collection/SetMixin/skipWhile.html">skipWhile</a></li>
        <li><a href="dart-collection/SetMixin/take.html">take</a></li>
        <li><a href="dart-collection/SetMixin/takeWhile.html">takeWhile</a></li>
        <li><a href="dart-collection/SetMixin/toList.html">toList</a></li>
        <li><a href="dart-collection/SetMixin/toSet.html">toSet</a></li>
        <li><a href="dart-collection/SetMixin/toString.html">toString</a></li>
        <li><a href="dart-collection/SetMixin/union.html">union</a></li>
        <li><a href="dart-collection/SetMixin/where.html">where</a></li>
        <li><a href="dart-collection/SetMixin/whereType.html">whereType</a></li>
        <li class="inherited"><a href="dart-core/Object/noSuchMethod.html">noSuchMethod</a></li>
    
        <li class="section-title inherited"><a href="dart-collection/SetMixin-class.html#operators">Operators</a></li>
        <li class="inherited"><a href="dart-core/Object/operator_equals.html">operator ==</a></li>
    
    
    
    
    
    </ol>
  </div><!--/.sidebar-offcanvas-->

  <div id="dartdoc-main-content" class="col-xs-12 col-sm-9 col-md-8 main-content">
      <div><h1><span class="kind-method">add</span> method</h1></div>

    <section class="multi-line-signature">
      <span class="returntype"><a href="dart-core/bool-class.html">bool</a></span>
            <span class="name ">add</span>
(<wbr><span class="parameter" id="add-param-value"><span class="type-annotation">E</span> <span class="parameter-name">value</span></span>)
      <div class="features">override</div>
    </section>
    <section class="desc markdown">
      <p>Adds <code>value</code> to the set.</p>
<p>Returns <code>true</code> if <code>value</code> (or an equal value) was not yet in the set.
Otherwise returns <code>false</code> and the set is not changed.</p>
<p>Example:</p>
<pre class="language-dart"><code class="language-dart">var set = Set();
var time1 = DateTime.fromMillisecondsSinceEpoch(0);
var time2 = DateTime.fromMillisecondsSinceEpoch(0);
// time1 and time2 are equal, but not identical.
assert(time1 == time2);
assert(!identical(time1, time2));
set.add(time1);  // =&gt; true.
// A value equal to time2 exists already in the set, and the call to
// add doesn't change the set.
set.add(time2);  // =&gt; false.
assert(set.length == 1);
assert(identical(time1, set.first));
</code></pre>
    </section>
    
    <section class="summary source-code" id="source">
      <h2><span>Implementation</span></h2>
      <pre class="language-dart"><code class="language-dart">bool add(E value);</code></pre>
    </section>

  </div> <!-- /.main-content -->

  <div id="dartdoc-sidebar-right" class="col-xs-6 col-sm-6 col-md-2 sidebar sidebar-offcanvas-right">
  </div><!--/.sidebar-offcanvas-->

</main>

<footer>
  <span class="no-break">
    gms_check
      1.0.0
  </span>

  <!-- footer-text placeholder -->
</footer>

<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.2.1/jquery.min.js"></script>
<script src="static-assets/typeahead.bundle.min.js"></script>
<script src="static-assets/highlight.pack.js"></script>
<script src="static-assets/URI.js"></script>
<script src="static-assets/script.js"></script>
<!-- footer placeholder -->

</body>

</html>
