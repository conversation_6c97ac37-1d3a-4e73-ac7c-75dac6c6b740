<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, height=device-height, initial-scale=1, user-scalable=no">
  <meta name="description" content="API docs for the updateAll method from the SplayTreeMap class, for the Dart programming language.">
  <title>updateAll method - SplayTreeMap class - dart:collection library - Dart API</title>
  <!-- required because all the links are pseudo-absolute -->
  <base href="../..">

  <link href="https://fonts.googleapis.com/css?family=Source+Code+Pro:500,400i,400,300|Source+Sans+Pro:400,300,700" rel="stylesheet">
  <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
  <link rel="stylesheet" href="static-assets/github.css">
  <link rel="stylesheet" href="static-assets/styles.css">
  <link rel="icon" href="static-assets/favicon.png">
  <!-- header placeholder -->
</head>

<body>

<div id="overlay-under-drawer"></div>

<header id="title">
  <button id="sidenav-left-toggle" type="button">&nbsp;</button>
  <ol class="breadcrumbs gt-separated dark hidden-xs">
    <li><a href="index.html">gms_check</a></li>
    <li><a href="dart-collection/dart-collection-library.html">dart:collection</a></li>
    <li><a href="dart-collection/SplayTreeMap-class.html">SplayTreeMap<span class="signature">&lt;<wbr><span class="type-parameter">K</span>, <span class="type-parameter">V</span>&gt;</span></a></li>
    <li class="self-crumb">updateAll method</li>
  </ol>
  <div class="self-name">updateAll</div>
  <form class="search navbar-right" role="search">
    <input type="text" id="search-box" autocomplete="off" disabled class="form-control typeahead" placeholder="Loading search...">
  </form>
</header>

<main>

  <div id="dartdoc-sidebar-left" class="col-xs-6 col-sm-3 col-md-2 sidebar sidebar-offcanvas-left">
    <header id="header-search-sidebar" class="hidden-l">
      <form class="search-sidebar" role="search">
        <input type="text" id="search-sidebar" autocomplete="off" disabled class="form-control typeahead" placeholder="Loading search...">
      </form>
    </header>
    
    <ol class="breadcrumbs gt-separated dark hidden-l" id="sidebar-nav">
      <li><a href="index.html">gms_check</a></li>
      <li><a href="dart-collection/dart-collection-library.html">dart:collection</a></li>
      <li><a href="dart-collection/SplayTreeMap-class.html">SplayTreeMap<span class="signature">&lt;<wbr><span class="type-parameter">K</span>, <span class="type-parameter">V</span>&gt;</span></a></li>
      <li class="self-crumb">updateAll method</li>
    </ol>
    
    <h5>SplayTreeMap class</h5>
    <ol>
    
        <li class="section-title"><a href="dart-collection/SplayTreeMap-class.html#constructors">Constructors</a></li>
        <li><a href="dart-collection/SplayTreeMap/SplayTreeMap.html">SplayTreeMap</a></li>
        <li><a href="dart-collection/SplayTreeMap/SplayTreeMap.from.html">from</a></li>
        <li><a href="dart-collection/SplayTreeMap/SplayTreeMap.fromIterable.html">fromIterable</a></li>
        <li><a href="dart-collection/SplayTreeMap/SplayTreeMap.fromIterables.html">fromIterables</a></li>
        <li><a href="dart-collection/SplayTreeMap/SplayTreeMap.of.html">of</a></li>
    
        <li class="section-title">
            <a href="dart-collection/SplayTreeMap-class.html#instance-properties">Properties</a>
        </li>
        <li><a href="dart-collection/SplayTreeMap/entries.html">entries</a></li>
        <li><a href="dart-collection/SplayTreeMap/isEmpty.html">isEmpty</a></li>
        <li><a href="dart-collection/SplayTreeMap/isNotEmpty.html">isNotEmpty</a></li>
        <li><a href="dart-collection/SplayTreeMap/keys.html">keys</a></li>
        <li><a href="dart-collection/SplayTreeMap/length.html">length</a></li>
        <li><a href="dart-collection/SplayTreeMap/values.html">values</a></li>
        <li class="inherited"><a href="dart-core/Object/hashCode.html">hashCode</a></li>
        <li class="inherited"><a href="dart-core/Object/runtimeType.html">runtimeType</a></li>
    
        <li class="section-title"><a href="dart-collection/SplayTreeMap-class.html#instance-methods">Methods</a></li>
        <li><a href="dart-collection/SplayTreeMap/addAll.html">addAll</a></li>
        <li><a href="dart-collection/SplayTreeMap/clear.html">clear</a></li>
        <li><a href="dart-collection/SplayTreeMap/containsKey.html">containsKey</a></li>
        <li><a href="dart-collection/SplayTreeMap/containsValue.html">containsValue</a></li>
        <li><a href="dart-collection/SplayTreeMap/firstKey.html">firstKey</a></li>
        <li><a href="dart-collection/SplayTreeMap/firstKeyAfter.html">firstKeyAfter</a></li>
        <li><a href="dart-collection/SplayTreeMap/forEach.html">forEach</a></li>
        <li><a href="dart-collection/SplayTreeMap/lastKey.html">lastKey</a></li>
        <li><a href="dart-collection/SplayTreeMap/lastKeyBefore.html">lastKeyBefore</a></li>
        <li><a href="dart-collection/SplayTreeMap/putIfAbsent.html">putIfAbsent</a></li>
        <li><a href="dart-collection/SplayTreeMap/remove.html">remove</a></li>
        <li><a href="dart-collection/SplayTreeMap/update.html">update</a></li>
        <li><a href="dart-collection/SplayTreeMap/updateAll.html">updateAll</a></li>
        <li class="inherited"><a href="dart-collection/MapMixin/addEntries.html">addEntries</a></li>
        <li class="inherited"><a href="dart-collection/MapMixin/cast.html">cast</a></li>
        <li class="inherited"><a href="dart-collection/MapMixin/map.html">map</a></li>
        <li class="inherited"><a href="dart-core/Object/noSuchMethod.html">noSuchMethod</a></li>
        <li class="inherited"><a href="dart-collection/MapMixin/removeWhere.html">removeWhere</a></li>
        <li class="inherited"><a href="dart-collection/MapMixin/toString.html">toString</a></li>
    
        <li class="section-title"><a href="dart-collection/SplayTreeMap-class.html#operators">Operators</a></li>
        <li><a href="dart-collection/SplayTreeMap/operator_get.html">operator []</a></li>
        <li><a href="dart-collection/SplayTreeMap/operator_put.html">operator []=</a></li>
        <li class="inherited"><a href="dart-core/Object/operator_equals.html">operator ==</a></li>
    
    
    
    
    
    </ol>
  </div><!--/.sidebar-offcanvas-->

  <div id="dartdoc-main-content" class="col-xs-12 col-sm-9 col-md-8 main-content">
      <div><h1><span class="kind-method">updateAll</span> method</h1></div>

    <section class="multi-line-signature">
      <span class="returntype">void</span>
            <span class="name ">updateAll</span>
(<wbr><span class="parameter" id="updateAll-param-update"><span class="type-annotation">V</span> <span class="parameter-name">update</span>(<span class="parameter" id="param-key"><span class="type-annotation">K</span> <span class="parameter-name">key</span>, </span> <span class="parameter" id="param-value"><span class="type-annotation">V</span> <span class="parameter-name">value</span></span>)</span>)
      <div class="features">override</div>
    </section>
    <section class="desc markdown">
      <p>Updates all values.</p>
<p>Iterates over all entries in the map and updates them with the result
of invoking <code>update</code>.</p>
    </section>
    
    <section class="summary source-code" id="source">
      <h2><span>Implementation</span></h2>
      <pre class="language-dart"><code class="language-dart">void updateAll(V update(K key, V value)) {
  var root = _root;
  if (root == null) return;
  var iterator = _SplayTreeMapEntryIterator(this);
  while (iterator.moveNext()) {
    var node = iterator.current;
    var newValue = update(node.key, node.value);
    iterator._replaceValue(newValue);
  }
}</code></pre>
    </section>

  </div> <!-- /.main-content -->

  <div id="dartdoc-sidebar-right" class="col-xs-6 col-sm-6 col-md-2 sidebar sidebar-offcanvas-right">
  </div><!--/.sidebar-offcanvas-->

</main>

<footer>
  <span class="no-break">
    gms_check
      1.0.0
  </span>

  <!-- footer-text placeholder -->
</footer>

<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.2.1/jquery.min.js"></script>
<script src="static-assets/typeahead.bundle.min.js"></script>
<script src="static-assets/highlight.pack.js"></script>
<script src="static-assets/URI.js"></script>
<script src="static-assets/script.js"></script>
<!-- footer placeholder -->

</body>

</html>
