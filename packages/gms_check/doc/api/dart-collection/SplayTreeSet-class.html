<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, height=device-height, initial-scale=1, user-scalable=no">
  <meta name="description" content="API docs for the SplayTreeSet class from the dart:collection library, for the Dart programming language.">
  <title>SplayTreeSet class - dart:collection library - Dart API</title>
  <!-- required because all the links are pseudo-absolute -->
  <base href="..">

  <link href="https://fonts.googleapis.com/css?family=Source+Code+Pro:500,400i,400,300|Source+Sans+Pro:400,300,700" rel="stylesheet">
  <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
  <link rel="stylesheet" href="static-assets/github.css">
  <link rel="stylesheet" href="static-assets/styles.css">
  <link rel="icon" href="static-assets/favicon.png">
  <!-- header placeholder -->
</head>

<body>

<div id="overlay-under-drawer"></div>

<header id="title">
  <button id="sidenav-left-toggle" type="button">&nbsp;</button>
  <ol class="breadcrumbs gt-separated dark hidden-xs">
    <li><a href="index.html">gms_check</a></li>
    <li><a href="dart-collection/dart-collection-library.html">dart:collection</a></li>
    <li class="self-crumb">SplayTreeSet<span class="signature">&lt;<wbr><span class="type-parameter">E</span>&gt;</span> class</li>
  </ol>
  <div class="self-name">SplayTreeSet</div>
  <form class="search navbar-right" role="search">
    <input type="text" id="search-box" autocomplete="off" disabled class="form-control typeahead" placeholder="Loading search...">
  </form>
</header>

<main>

  <div id="dartdoc-sidebar-left" class="col-xs-6 col-sm-3 col-md-2 sidebar sidebar-offcanvas-left">
    <header id="header-search-sidebar" class="hidden-l">
      <form class="search-sidebar" role="search">
        <input type="text" id="search-sidebar" autocomplete="off" disabled class="form-control typeahead" placeholder="Loading search...">
      </form>
    </header>
    
    <ol class="breadcrumbs gt-separated dark hidden-l" id="sidebar-nav">
      <li><a href="index.html">gms_check</a></li>
      <li><a href="dart-collection/dart-collection-library.html">dart:collection</a></li>
      <li class="self-crumb">SplayTreeSet<span class="signature">&lt;<wbr><span class="type-parameter">E</span>&gt;</span> class</li>
    </ol>
    
    <h5>dart:collection library</h5>
    <ol>
      <li class="section-title"><a href="dart-collection/dart-collection-library.html#classes">Classes</a></li>
      <li><a href="dart-collection/DoubleLinkedQueue-class.html">DoubleLinkedQueue</a></li>
      <li><a href="dart-collection/DoubleLinkedQueueEntry-class.html">DoubleLinkedQueueEntry</a></li>
      <li><a href="dart-collection/HashMap-class.html">HashMap</a></li>
      <li><a href="dart-collection/HashSet-class.html">HashSet</a></li>
      <li><a href="dart-collection/HasNextIterator-class.html">HasNextIterator</a></li>
      <li><a href="dart-collection/IterableBase-class.html">IterableBase</a></li>
      <li><a href="dart-collection/IterableMixin-class.html">IterableMixin</a></li>
      <li><a href="dart-collection/LinkedHashMap-class.html">LinkedHashMap</a></li>
      <li><a href="dart-collection/LinkedHashSet-class.html">LinkedHashSet</a></li>
      <li><a href="dart-collection/LinkedList-class.html">LinkedList</a></li>
      <li><a href="dart-collection/LinkedListEntry-class.html">LinkedListEntry</a></li>
      <li><a href="dart-collection/ListBase-class.html">ListBase</a></li>
      <li><a href="dart-collection/ListMixin-class.html">ListMixin</a></li>
      <li><a href="dart-collection/ListQueue-class.html">ListQueue</a></li>
      <li><a href="dart-collection/MapBase-class.html">MapBase</a></li>
      <li><a href="dart-collection/MapMixin-class.html">MapMixin</a></li>
      <li><a href="dart-collection/MapView-class.html">MapView</a></li>
      <li><a href="dart-collection/Queue-class.html">Queue</a></li>
      <li><a href="dart-collection/SetBase-class.html">SetBase</a></li>
      <li><a href="dart-collection/SetMixin-class.html">SetMixin</a></li>
      <li><a href="dart-collection/SplayTreeMap-class.html">SplayTreeMap</a></li>
      <li><a href="dart-collection/SplayTreeSet-class.html">SplayTreeSet</a></li>
      <li><a href="dart-collection/UnmodifiableListView-class.html">UnmodifiableListView</a></li>
      <li><a href="dart-collection/UnmodifiableMapBase-class.html">UnmodifiableMapBase</a></li>
      <li><a href="dart-collection/UnmodifiableMapView-class.html">UnmodifiableMapView</a></li>
      <li><a href="dart-collection/UnmodifiableSetView-class.html">UnmodifiableSetView</a></li>
    
    
    
    
    
    
    
    
    </ol>
  </div>

  <div id="dartdoc-main-content" class="col-xs-12 col-sm-9 col-md-8 main-content">
      <div><h1><span class="kind-class">SplayTreeSet&lt;<wbr><span class="type-parameter">E</span>&gt;</span> class </h1></div>

    <section class="desc markdown">
      <p>A <a href="dart-core/Set-class.html">Set</a> of objects that can be ordered relative to each other.</p>
<p>The set is based on a self-balancing binary tree. It allows most operations
in amortized logarithmic time.</p>
<p>Elements of the set are compared using the <code>compare</code> function passed in
the constructor, both for ordering and for equality.
If the set contains only an object <code>a</code>, then <code>set.contains(b)</code>
will return <code>true</code> if and only if <code>compare(a, b) == 0</code>,
and the value of <code>a == b</code> is not even checked.
If the compare function is omitted, the objects are assumed to be
<a href="dart-core/Comparable-class.html">Comparable</a>, and are compared using their <a href="dart-core/Comparable/compareTo.html">Comparable.compareTo</a> method.
Non-comparable objects (including <code>null</code>) will not work as an element
in that case.</p>
    </section>
    
    <section>
      <dl class="dl-horizontal">


        <dt>Mixed in types</dt>
        <dd><ul class="comma-separated clazz-relationships">
          <li><a href="dart-collection/IterableMixin-class.html">IterableMixin</a><span class="signature">&lt;<wbr><span class="type-parameter">E</span>&gt;</span></li>
          <li><a href="dart-collection/SetMixin-class.html">SetMixin</a><span class="signature">&lt;<wbr><span class="type-parameter">E</span>&gt;</span></li>
        </ul></dd>



      </dl>
    </section>

    <section class="summary offset-anchor" id="constructors">
      <h2>Constructors</h2>

      <dl class="constructor-summary-list">
        <dt id="SplayTreeSet" class="callable">
          <span class="name"><a href="dart-collection/SplayTreeSet/SplayTreeSet.html">SplayTreeSet</a></span><span class="signature">([<span class="parameter" id="-param-compare"><span class="type-annotation"><a href="dart-core/int-class.html">int</a></span> <span class="parameter-name">compare</span>(<span class="parameter" id="param-key1"><span class="type-annotation">E</span> <span class="parameter-name">key1</span></span> <span class="parameter" id="param-key2"><span class="type-annotation">E</span> <span class="parameter-name">key2</span></span>), </span> <span class="parameter" id="-param-isValidKey"><span class="type-annotation"><a href="dart-core/bool-class.html">bool</a></span> <span class="parameter-name">isValidKey</span>(<span class="parameter" id="param-potentialKey"><span class="type-annotation">dynamic</span> <span class="parameter-name">potentialKey</span></span>)</span> ])</span>
        </dt>
        <dd>
          Create a new <a href="dart-collection/SplayTreeSet-class.html">SplayTreeSet</a> with the given compare function. <a href="dart-collection/SplayTreeSet/SplayTreeSet.html">[...]</a>
        </dd>
        <dt id="SplayTreeSet.from" class="callable">
          <span class="name"><a href="dart-collection/SplayTreeSet/SplayTreeSet.from.html">SplayTreeSet.from</a></span><span class="signature">(<span class="parameter" id="from-param-elements"><span class="type-annotation"><a href="dart-core/Iterable-class.html">Iterable</a></span> <span class="parameter-name">elements</span>, [</span> <span class="parameter" id="from-param-compare"><span class="type-annotation"><a href="dart-core/int-class.html">int</a></span> <span class="parameter-name">compare</span>(<span class="parameter" id="param-key1"><span class="type-annotation">E</span> <span class="parameter-name">key1</span></span> <span class="parameter" id="param-key2"><span class="type-annotation">E</span> <span class="parameter-name">key2</span></span>), </span> <span class="parameter" id="from-param-isValidKey"><span class="type-annotation"><a href="dart-core/bool-class.html">bool</a></span> <span class="parameter-name">isValidKey</span>(<span class="parameter" id="param-potentialKey"><span class="type-annotation">dynamic</span> <span class="parameter-name">potentialKey</span></span>)</span> ])</span>
        </dt>
        <dd>
          Creates a <a href="dart-collection/SplayTreeSet-class.html">SplayTreeSet</a> that contains all <code>elements</code>. <a href="dart-collection/SplayTreeSet/SplayTreeSet.from.html">[...]</a>
          <div class="constructor-modifier features">factory</div>
        </dd>
        <dt id="SplayTreeSet.of" class="callable">
          <span class="name"><a href="dart-collection/SplayTreeSet/SplayTreeSet.of.html">SplayTreeSet.of</a></span><span class="signature">(<span class="parameter" id="of-param-elements"><span class="type-annotation"><a href="dart-core/Iterable-class.html">Iterable</a><span class="signature">&lt;<wbr><span class="type-parameter">E</span>&gt;</span></span> <span class="parameter-name">elements</span>, [</span> <span class="parameter" id="of-param-compare"><span class="type-annotation"><a href="dart-core/int-class.html">int</a></span> <span class="parameter-name">compare</span>(<span class="parameter" id="param-key1"><span class="type-annotation">E</span> <span class="parameter-name">key1</span></span> <span class="parameter" id="param-key2"><span class="type-annotation">E</span> <span class="parameter-name">key2</span></span>), </span> <span class="parameter" id="of-param-isValidKey"><span class="type-annotation"><a href="dart-core/bool-class.html">bool</a></span> <span class="parameter-name">isValidKey</span>(<span class="parameter" id="param-potentialKey"><span class="type-annotation">dynamic</span> <span class="parameter-name">potentialKey</span></span>)</span> ])</span>
        </dt>
        <dd>
          Creates a <a href="dart-collection/SplayTreeSet-class.html">SplayTreeSet</a> from <code>elements</code>. <a href="dart-collection/SplayTreeSet/SplayTreeSet.of.html">[...]</a>
          <div class="constructor-modifier features">factory</div>
        </dd>
      </dl>
    </section>

    <section class="summary offset-anchor" id="instance-properties">
      <h2>Properties</h2>

      <dl class="properties">
        <dt id="first" class="property">
          <span class="name"><a href="dart-collection/SplayTreeSet/first.html">first</a></span>
          <span class="signature">&#8594; E</span>         
        </dt>
        <dd>
          Returns the first element. <a href="dart-collection/SplayTreeSet/first.html">[...]</a>
                  <div class="features">read-only, override</div>
</dd>
        <dt id="isEmpty" class="property">
          <span class="name"><a href="dart-collection/SplayTreeSet/isEmpty.html">isEmpty</a></span>
          <span class="signature">&#8594; <a href="dart-core/bool-class.html">bool</a></span>         
        </dt>
        <dd>
          Returns <code>true</code> if there are no elements in this collection. <a href="dart-collection/SplayTreeSet/isEmpty.html">[...]</a>
                  <div class="features">read-only, override</div>
</dd>
        <dt id="isNotEmpty" class="property">
          <span class="name"><a href="dart-collection/SplayTreeSet/isNotEmpty.html">isNotEmpty</a></span>
          <span class="signature">&#8594; <a href="dart-core/bool-class.html">bool</a></span>         
        </dt>
        <dd>
          Returns true if there is at least one element in this collection. <a href="dart-collection/SplayTreeSet/isNotEmpty.html">[...]</a>
                  <div class="features">read-only, override</div>
</dd>
        <dt id="iterator" class="property">
          <span class="name"><a href="dart-collection/SplayTreeSet/iterator.html">iterator</a></span>
          <span class="signature">&#8594; <a href="dart-core/Iterator-class.html">Iterator</a><span class="signature">&lt;<wbr><span class="type-parameter">E</span>&gt;</span></span>         
        </dt>
        <dd>
          An iterator that iterates over the elements of this set. <a href="dart-collection/SplayTreeSet/iterator.html">[...]</a>
                  <div class="features">read-only, override</div>
</dd>
        <dt id="last" class="property">
          <span class="name"><a href="dart-collection/SplayTreeSet/last.html">last</a></span>
          <span class="signature">&#8594; E</span>         
        </dt>
        <dd>
          Returns the last element. <a href="dart-collection/SplayTreeSet/last.html">[...]</a>
                  <div class="features">read-only, override</div>
</dd>
        <dt id="length" class="property">
          <span class="name"><a href="dart-collection/SplayTreeSet/length.html">length</a></span>
          <span class="signature">&#8594; <a href="dart-core/int-class.html">int</a></span>         
        </dt>
        <dd>
          Returns the number of elements in <a href="dart-collection/SplayTreeSet-class.html">this</a>. <a href="dart-collection/SplayTreeSet/length.html">[...]</a>
                  <div class="features">read-only, override</div>
</dd>
        <dt id="single" class="property">
          <span class="name"><a href="dart-collection/SplayTreeSet/single.html">single</a></span>
          <span class="signature">&#8594; E</span>         
        </dt>
        <dd>
          Checks that this iterable has only one element, and returns that element. <a href="dart-collection/SplayTreeSet/single.html">[...]</a>
                  <div class="features">read-only, override</div>
</dd>
        <dt id="hashCode" class="property inherited">
          <span class="name"><a href="dart-core/Object/hashCode.html">hashCode</a></span>
          <span class="signature">&#8594; <a href="dart-core/int-class.html">int</a></span>         
        </dt>
        <dd class="inherited">
          The hash code for this object. <a href="dart-core/Object/hashCode.html">[...]</a>
                  <div class="features">read-only, inherited</div>
</dd>
        <dt id="runtimeType" class="property inherited">
          <span class="name"><a href="dart-core/Object/runtimeType.html">runtimeType</a></span>
          <span class="signature">&#8594; <a href="dart-core/Type-class.html">Type</a></span>         
        </dt>
        <dd class="inherited">
          A representation of the runtime type of the object.
                  <div class="features">read-only, inherited</div>
</dd>
      </dl>
    </section>

    <section class="summary offset-anchor" id="instance-methods">
      <h2>Methods</h2>
      <dl class="callables">
        <dt id="add" class="callable">
          <span class="name"><a href="dart-collection/SplayTreeSet/add.html">add</a></span><span class="signature">(<wbr><span class="parameter" id="add-param-element"><span class="type-annotation">E</span> <span class="parameter-name">element</span></span>)
            <span class="returntype parameter">&#8594; <a href="dart-core/bool-class.html">bool</a></span>
          </span>
                  </dt>
        <dd>
          Adds <code>value</code> to the set. <a href="dart-collection/SplayTreeSet/add.html">[...]</a>
                  <div class="features">override</div>
</dd>
        <dt id="addAll" class="callable">
          <span class="name"><a href="dart-collection/SplayTreeSet/addAll.html">addAll</a></span><span class="signature">(<wbr><span class="parameter" id="addAll-param-elements"><span class="type-annotation"><a href="dart-core/Iterable-class.html">Iterable</a><span class="signature">&lt;<wbr><span class="type-parameter">E</span>&gt;</span></span> <span class="parameter-name">elements</span></span>)
            <span class="returntype parameter">&#8594; void</span>
          </span>
                  </dt>
        <dd>
          Adds all <code>elements</code> to this set. <a href="dart-collection/SplayTreeSet/addAll.html">[...]</a>
                  <div class="features">override</div>
</dd>
        <dt id="cast" class="callable">
          <span class="name"><a href="dart-collection/SplayTreeSet/cast.html">cast</a></span><span class="signature">&lt;<wbr><span class="type-parameter">R</span>&gt;</span><span class="signature">(<wbr>)
            <span class="returntype parameter">&#8594; <a href="dart-core/Set-class.html">Set</a><span class="signature">&lt;<wbr><span class="type-parameter">R</span>&gt;</span></span>
          </span>
                  </dt>
        <dd>
          Provides a view of this iterable as an iterable of <code>R</code> instances. <a href="dart-collection/SplayTreeSet/cast.html">[...]</a>
                  <div class="features">override</div>
</dd>
        <dt id="clear" class="callable">
          <span class="name"><a href="dart-collection/SplayTreeSet/clear.html">clear</a></span><span class="signature">(<wbr>)
            <span class="returntype parameter">&#8594; void</span>
          </span>
                  </dt>
        <dd>
          Removes all elements from the set.
                  <div class="features">override</div>
</dd>
        <dt id="contains" class="callable">
          <span class="name"><a href="dart-collection/SplayTreeSet/contains.html">contains</a></span><span class="signature">(<wbr><span class="parameter" id="contains-param-element"><span class="type-annotation"><a href="dart-core/Object-class.html">Object</a></span> <span class="parameter-name">element</span></span>)
            <span class="returntype parameter">&#8594; <a href="dart-core/bool-class.html">bool</a></span>
          </span>
                  </dt>
        <dd>
          Whether the collection contains an element equal to <code>element</code>. <a href="dart-collection/SplayTreeSet/contains.html">[...]</a>
                  <div class="features">override</div>
</dd>
        <dt id="difference" class="callable">
          <span class="name"><a href="dart-collection/SplayTreeSet/difference.html">difference</a></span><span class="signature">(<wbr><span class="parameter" id="difference-param-other"><span class="type-annotation"><a href="dart-core/Set-class.html">Set</a><span class="signature">&lt;<wbr><span class="type-parameter"><a href="dart-core/Object-class.html">Object</a></span>&gt;</span></span> <span class="parameter-name">other</span></span>)
            <span class="returntype parameter">&#8594; <a href="dart-core/Set-class.html">Set</a><span class="signature">&lt;<wbr><span class="type-parameter">E</span>&gt;</span></span>
          </span>
                  </dt>
        <dd>
          Creates a new set with the elements of this that are not in <code>other</code>. <a href="dart-collection/SplayTreeSet/difference.html">[...]</a>
                  <div class="features">override</div>
</dd>
        <dt id="intersection" class="callable">
          <span class="name"><a href="dart-collection/SplayTreeSet/intersection.html">intersection</a></span><span class="signature">(<wbr><span class="parameter" id="intersection-param-other"><span class="type-annotation"><a href="dart-core/Set-class.html">Set</a><span class="signature">&lt;<wbr><span class="type-parameter"><a href="dart-core/Object-class.html">Object</a></span>&gt;</span></span> <span class="parameter-name">other</span></span>)
            <span class="returntype parameter">&#8594; <a href="dart-core/Set-class.html">Set</a><span class="signature">&lt;<wbr><span class="type-parameter">E</span>&gt;</span></span>
          </span>
                  </dt>
        <dd>
          Creates a new set which is the intersection between this set and <code>other</code>. <a href="dart-collection/SplayTreeSet/intersection.html">[...]</a>
                  <div class="features">override</div>
</dd>
        <dt id="lookup" class="callable">
          <span class="name"><a href="dart-collection/SplayTreeSet/lookup.html">lookup</a></span><span class="signature">(<wbr><span class="parameter" id="lookup-param-object"><span class="type-annotation"><a href="dart-core/Object-class.html">Object</a></span> <span class="parameter-name">object</span></span>)
            <span class="returntype parameter">&#8594; E</span>
          </span>
                  </dt>
        <dd>
          If an object equal to <code>object</code> is in the set, return it. <a href="dart-collection/SplayTreeSet/lookup.html">[...]</a>
                  <div class="features">override</div>
</dd>
        <dt id="remove" class="callable">
          <span class="name"><a href="dart-collection/SplayTreeSet/remove.html">remove</a></span><span class="signature">(<wbr><span class="parameter" id="remove-param-object"><span class="type-annotation"><a href="dart-core/Object-class.html">Object</a></span> <span class="parameter-name">object</span></span>)
            <span class="returntype parameter">&#8594; <a href="dart-core/bool-class.html">bool</a></span>
          </span>
                  </dt>
        <dd>
          Removes <code>value</code> from the set. <a href="dart-collection/SplayTreeSet/remove.html">[...]</a>
                  <div class="features">override</div>
</dd>
        <dt id="removeAll" class="callable">
          <span class="name"><a href="dart-collection/SplayTreeSet/removeAll.html">removeAll</a></span><span class="signature">(<wbr><span class="parameter" id="removeAll-param-elements"><span class="type-annotation"><a href="dart-core/Iterable-class.html">Iterable</a><span class="signature">&lt;<wbr><span class="type-parameter"><a href="dart-core/Object-class.html">Object</a></span>&gt;</span></span> <span class="parameter-name">elements</span></span>)
            <span class="returntype parameter">&#8594; void</span>
          </span>
                  </dt>
        <dd>
          Removes each element of <code>elements</code> from this set.
                  <div class="features">override</div>
</dd>
        <dt id="retainAll" class="callable">
          <span class="name"><a href="dart-collection/SplayTreeSet/retainAll.html">retainAll</a></span><span class="signature">(<wbr><span class="parameter" id="retainAll-param-elements"><span class="type-annotation"><a href="dart-core/Iterable-class.html">Iterable</a><span class="signature">&lt;<wbr><span class="type-parameter"><a href="dart-core/Object-class.html">Object</a></span>&gt;</span></span> <span class="parameter-name">elements</span></span>)
            <span class="returntype parameter">&#8594; void</span>
          </span>
                  </dt>
        <dd>
          Removes all elements of this set that are not elements in <code>elements</code>. <a href="dart-collection/SplayTreeSet/retainAll.html">[...]</a>
                  <div class="features">override</div>
</dd>
        <dt id="toSet" class="callable">
          <span class="name"><a href="dart-collection/SplayTreeSet/toSet.html">toSet</a></span><span class="signature">(<wbr>)
            <span class="returntype parameter">&#8594; <a href="dart-core/Set-class.html">Set</a><span class="signature">&lt;<wbr><span class="type-parameter">E</span>&gt;</span></span>
          </span>
                  </dt>
        <dd>
          Creates a <a href="dart-core/Set-class.html">Set</a> containing the same elements as this iterable. <a href="dart-collection/SplayTreeSet/toSet.html">[...]</a>
                  <div class="features">override</div>
</dd>
        <dt id="toString" class="callable">
          <span class="name"><a href="dart-collection/SplayTreeSet/toString.html">toString</a></span><span class="signature">(<wbr>)
            <span class="returntype parameter">&#8594; <a href="dart-core/String-class.html">String</a></span>
          </span>
                  </dt>
        <dd>
          A string representation of this object. <a href="dart-collection/SplayTreeSet/toString.html">[...]</a>
                  <div class="features">override</div>
</dd>
        <dt id="union" class="callable">
          <span class="name"><a href="dart-collection/SplayTreeSet/union.html">union</a></span><span class="signature">(<wbr><span class="parameter" id="union-param-other"><span class="type-annotation"><a href="dart-core/Set-class.html">Set</a><span class="signature">&lt;<wbr><span class="type-parameter">E</span>&gt;</span></span> <span class="parameter-name">other</span></span>)
            <span class="returntype parameter">&#8594; <a href="dart-core/Set-class.html">Set</a><span class="signature">&lt;<wbr><span class="type-parameter">E</span>&gt;</span></span>
          </span>
                  </dt>
        <dd>
          Creates a new set which contains all the elements of this set and <code>other</code>. <a href="dart-collection/SplayTreeSet/union.html">[...]</a>
                  <div class="features">override</div>
</dd>
        <dt id="any" class="callable inherited">
          <span class="name"><a href="dart-collection/SetMixin/any.html">any</a></span><span class="signature">(<wbr><span class="parameter" id="any-param-test"><span class="type-annotation"><a href="dart-core/bool-class.html">bool</a></span> <span class="parameter-name">test</span>(<span class="parameter" id="param-element"><span class="type-annotation">E</span> <span class="parameter-name">element</span></span>)</span>)
            <span class="returntype parameter">&#8594; <a href="dart-core/bool-class.html">bool</a></span>
          </span>
                  </dt>
        <dd class="inherited">
          Checks whether any element of this iterable satisfies <code>test</code>. <a href="dart-collection/SetMixin/any.html">[...]</a>
                  <div class="features">inherited</div>
</dd>
        <dt id="containsAll" class="callable inherited">
          <span class="name"><a href="dart-collection/SetMixin/containsAll.html">containsAll</a></span><span class="signature">(<wbr><span class="parameter" id="containsAll-param-other"><span class="type-annotation"><a href="dart-core/Iterable-class.html">Iterable</a><span class="signature">&lt;<wbr><span class="type-parameter"><a href="dart-core/Object-class.html">Object</a></span>&gt;</span></span> <span class="parameter-name">other</span></span>)
            <span class="returntype parameter">&#8594; <a href="dart-core/bool-class.html">bool</a></span>
          </span>
                  </dt>
        <dd class="inherited">
          Whether this set contains all the elements of <code>other</code>.
                  <div class="features">inherited</div>
</dd>
        <dt id="elementAt" class="callable inherited">
          <span class="name"><a href="dart-collection/SetMixin/elementAt.html">elementAt</a></span><span class="signature">(<wbr><span class="parameter" id="elementAt-param-index"><span class="type-annotation"><a href="dart-core/int-class.html">int</a></span> <span class="parameter-name">index</span></span>)
            <span class="returntype parameter">&#8594; E</span>
          </span>
                  </dt>
        <dd class="inherited">
          Returns the <code>index</code>th element. <a href="dart-collection/SetMixin/elementAt.html">[...]</a>
                  <div class="features">inherited</div>
</dd>
        <dt id="every" class="callable inherited">
          <span class="name"><a href="dart-collection/SetMixin/every.html">every</a></span><span class="signature">(<wbr><span class="parameter" id="every-param-f"><span class="type-annotation"><a href="dart-core/bool-class.html">bool</a></span> <span class="parameter-name">f</span>(<span class="parameter" id="param-element"><span class="type-annotation">E</span> <span class="parameter-name">element</span></span>)</span>)
            <span class="returntype parameter">&#8594; <a href="dart-core/bool-class.html">bool</a></span>
          </span>
                  </dt>
        <dd class="inherited">
          Checks whether every element of this iterable satisfies <code>test</code>. <a href="dart-collection/SetMixin/every.html">[...]</a>
                  <div class="features">inherited</div>
</dd>
        <dt id="expand" class="callable inherited">
          <span class="name"><a href="dart-collection/SetMixin/expand.html">expand</a></span><span class="signature">&lt;<wbr><span class="type-parameter">T</span>&gt;</span><span class="signature">(<wbr><span class="parameter" id="expand-param-f"><span class="type-annotation"><a href="dart-core/Iterable-class.html">Iterable</a><span class="signature">&lt;<wbr><span class="type-parameter">T</span>&gt;</span></span> <span class="parameter-name">f</span>(<span class="parameter" id="param-element"><span class="type-annotation">E</span> <span class="parameter-name">element</span></span>)</span>)
            <span class="returntype parameter">&#8594; <a href="dart-core/Iterable-class.html">Iterable</a><span class="signature">&lt;<wbr><span class="type-parameter">T</span>&gt;</span></span>
          </span>
                  </dt>
        <dd class="inherited">
          Expands each element of this <a href="dart-core/Iterable-class.html">Iterable</a> into zero or more elements. <a href="dart-collection/SetMixin/expand.html">[...]</a>
                  <div class="features">inherited</div>
</dd>
        <dt id="firstWhere" class="callable inherited">
          <span class="name"><a href="dart-collection/SetMixin/firstWhere.html">firstWhere</a></span><span class="signature">(<wbr><span class="parameter" id="firstWhere-param-test"><span class="type-annotation"><a href="dart-core/bool-class.html">bool</a></span> <span class="parameter-name">test</span>(<span class="parameter" id="param-value"><span class="type-annotation">E</span> <span class="parameter-name">value</span></span>), {</span> <span class="parameter" id="firstWhere-param-orElse"><span class="type-annotation">E</span> <span class="parameter-name">orElse</span>()</span> })
            <span class="returntype parameter">&#8594; E</span>
          </span>
                  </dt>
        <dd class="inherited">
          Returns the first element that satisfies the given predicate <code>test</code>. <a href="dart-collection/SetMixin/firstWhere.html">[...]</a>
                  <div class="features">inherited</div>
</dd>
        <dt id="fold" class="callable inherited">
          <span class="name"><a href="dart-collection/SetMixin/fold.html">fold</a></span><span class="signature">&lt;<wbr><span class="type-parameter">T</span>&gt;</span><span class="signature">(<wbr><span class="parameter" id="fold-param-initialValue"><span class="type-annotation">T</span> <span class="parameter-name">initialValue</span>, </span> <span class="parameter" id="fold-param-combine"><span class="type-annotation">T</span> <span class="parameter-name">combine</span>(<span class="parameter" id="param-previousValue"><span class="type-annotation">T</span> <span class="parameter-name">previousValue</span>, </span> <span class="parameter" id="param-element"><span class="type-annotation">E</span> <span class="parameter-name">element</span></span>)</span>)
            <span class="returntype parameter">&#8594; T</span>
          </span>
                  </dt>
        <dd class="inherited">
          Reduces a collection to a single value by iteratively combining each
element of the collection with an existing value <a href="dart-collection/SetMixin/fold.html">[...]</a>
                  <div class="features">inherited</div>
</dd>
        <dt id="followedBy" class="callable inherited">
          <span class="name"><a href="dart-collection/SetMixin/followedBy.html">followedBy</a></span><span class="signature">(<wbr><span class="parameter" id="followedBy-param-other"><span class="type-annotation"><a href="dart-core/Iterable-class.html">Iterable</a><span class="signature">&lt;<wbr><span class="type-parameter">E</span>&gt;</span></span> <span class="parameter-name">other</span></span>)
            <span class="returntype parameter">&#8594; <a href="dart-core/Iterable-class.html">Iterable</a><span class="signature">&lt;<wbr><span class="type-parameter">E</span>&gt;</span></span>
          </span>
                  </dt>
        <dd class="inherited">
          Returns the lazy concatenation of this iterable and <code>other</code>. <a href="dart-collection/SetMixin/followedBy.html">[...]</a>
                  <div class="features">inherited</div>
</dd>
        <dt id="forEach" class="callable inherited">
          <span class="name"><a href="dart-collection/SetMixin/forEach.html">forEach</a></span><span class="signature">(<wbr><span class="parameter" id="forEach-param-f"><span class="type-annotation">void</span> <span class="parameter-name">f</span>(<span class="parameter" id="param-element"><span class="type-annotation">E</span> <span class="parameter-name">element</span></span>)</span>)
            <span class="returntype parameter">&#8594; void</span>
          </span>
                  </dt>
        <dd class="inherited">
          Invokes <code>action</code> on each element of this iterable in iteration order.
                  <div class="features">inherited</div>
</dd>
        <dt id="join" class="callable inherited">
          <span class="name"><a href="dart-collection/SetMixin/join.html">join</a></span><span class="signature">(<wbr>[<span class="parameter" id="join-param-separator"><span class="type-annotation"><a href="dart-core/String-class.html">String</a></span> <span class="parameter-name">separator</span> = <span class="default-value">""</span></span> ])
            <span class="returntype parameter">&#8594; <a href="dart-core/String-class.html">String</a></span>
          </span>
                  </dt>
        <dd class="inherited">
          Converts each element to a <a href="dart-core/String-class.html">String</a> and concatenates the strings. <a href="dart-collection/SetMixin/join.html">[...]</a>
                  <div class="features">inherited</div>
</dd>
        <dt id="lastWhere" class="callable inherited">
          <span class="name"><a href="dart-collection/SetMixin/lastWhere.html">lastWhere</a></span><span class="signature">(<wbr><span class="parameter" id="lastWhere-param-test"><span class="type-annotation"><a href="dart-core/bool-class.html">bool</a></span> <span class="parameter-name">test</span>(<span class="parameter" id="param-value"><span class="type-annotation">E</span> <span class="parameter-name">value</span></span>), {</span> <span class="parameter" id="lastWhere-param-orElse"><span class="type-annotation">E</span> <span class="parameter-name">orElse</span>()</span> })
            <span class="returntype parameter">&#8594; E</span>
          </span>
                  </dt>
        <dd class="inherited">
          Returns the last element that satisfies the given predicate <code>test</code>. <a href="dart-collection/SetMixin/lastWhere.html">[...]</a>
                  <div class="features">inherited</div>
</dd>
        <dt id="map" class="callable inherited">
          <span class="name"><a href="dart-collection/SetMixin/map.html">map</a></span><span class="signature">&lt;<wbr><span class="type-parameter">T</span>&gt;</span><span class="signature">(<wbr><span class="parameter" id="map-param-f"><span class="type-annotation">T</span> <span class="parameter-name">f</span>(<span class="parameter" id="param-element"><span class="type-annotation">E</span> <span class="parameter-name">element</span></span>)</span>)
            <span class="returntype parameter">&#8594; <a href="dart-core/Iterable-class.html">Iterable</a><span class="signature">&lt;<wbr><span class="type-parameter">T</span>&gt;</span></span>
          </span>
                  </dt>
        <dd class="inherited">
          The current elements of this iterable modified by <code>toElement</code>. <a href="dart-collection/SetMixin/map.html">[...]</a>
                  <div class="features">inherited</div>
</dd>
        <dt id="noSuchMethod" class="callable inherited">
          <span class="name"><a href="dart-core/Object/noSuchMethod.html">noSuchMethod</a></span><span class="signature">(<wbr><span class="parameter" id="noSuchMethod-param-invocation"><span class="type-annotation"><a href="dart-core/Invocation-class.html">Invocation</a></span> <span class="parameter-name">invocation</span></span>)
            <span class="returntype parameter">&#8594; dynamic</span>
          </span>
                  </dt>
        <dd class="inherited">
          Invoked when a non-existent method or property is accessed. <a href="dart-core/Object/noSuchMethod.html">[...]</a>
                  <div class="features">inherited</div>
</dd>
        <dt id="reduce" class="callable inherited">
          <span class="name"><a href="dart-collection/SetMixin/reduce.html">reduce</a></span><span class="signature">(<wbr><span class="parameter" id="reduce-param-combine"><span class="type-annotation">E</span> <span class="parameter-name">combine</span>(<span class="parameter" id="param-value"><span class="type-annotation">E</span> <span class="parameter-name">value</span></span> <span class="parameter" id="param-element"><span class="type-annotation">E</span> <span class="parameter-name">element</span></span>)</span>)
            <span class="returntype parameter">&#8594; E</span>
          </span>
                  </dt>
        <dd class="inherited">
          Reduces a collection to a single value by iteratively combining elements
of the collection using the provided function. <a href="dart-collection/SetMixin/reduce.html">[...]</a>
                  <div class="features">inherited</div>
</dd>
        <dt id="removeWhere" class="callable inherited">
          <span class="name"><a href="dart-collection/SetMixin/removeWhere.html">removeWhere</a></span><span class="signature">(<wbr><span class="parameter" id="removeWhere-param-test"><span class="type-annotation"><a href="dart-core/bool-class.html">bool</a></span> <span class="parameter-name">test</span>(<span class="parameter" id="param-element"><span class="type-annotation">E</span> <span class="parameter-name">element</span></span>)</span>)
            <span class="returntype parameter">&#8594; void</span>
          </span>
                  </dt>
        <dd class="inherited">
          Removes all elements of this set that satisfy <code>test</code>.
                  <div class="features">inherited</div>
</dd>
        <dt id="retainWhere" class="callable inherited">
          <span class="name"><a href="dart-collection/SetMixin/retainWhere.html">retainWhere</a></span><span class="signature">(<wbr><span class="parameter" id="retainWhere-param-test"><span class="type-annotation"><a href="dart-core/bool-class.html">bool</a></span> <span class="parameter-name">test</span>(<span class="parameter" id="param-element"><span class="type-annotation">E</span> <span class="parameter-name">element</span></span>)</span>)
            <span class="returntype parameter">&#8594; void</span>
          </span>
                  </dt>
        <dd class="inherited">
          Removes all elements of this set that fail to satisfy <code>test</code>.
                  <div class="features">inherited</div>
</dd>
        <dt id="singleWhere" class="callable inherited">
          <span class="name"><a href="dart-collection/SetMixin/singleWhere.html">singleWhere</a></span><span class="signature">(<wbr><span class="parameter" id="singleWhere-param-test"><span class="type-annotation"><a href="dart-core/bool-class.html">bool</a></span> <span class="parameter-name">test</span>(<span class="parameter" id="param-value"><span class="type-annotation">E</span> <span class="parameter-name">value</span></span>), {</span> <span class="parameter" id="singleWhere-param-orElse"><span class="type-annotation">E</span> <span class="parameter-name">orElse</span>()</span> })
            <span class="returntype parameter">&#8594; E</span>
          </span>
                  </dt>
        <dd class="inherited">
          Returns the single element that satisfies <code>test</code>. <a href="dart-collection/SetMixin/singleWhere.html">[...]</a>
                  <div class="features">inherited</div>
</dd>
        <dt id="skip" class="callable inherited">
          <span class="name"><a href="dart-collection/SetMixin/skip.html">skip</a></span><span class="signature">(<wbr><span class="parameter" id="skip-param-n"><span class="type-annotation"><a href="dart-core/int-class.html">int</a></span> <span class="parameter-name">n</span></span>)
            <span class="returntype parameter">&#8594; <a href="dart-core/Iterable-class.html">Iterable</a><span class="signature">&lt;<wbr><span class="type-parameter">E</span>&gt;</span></span>
          </span>
                  </dt>
        <dd class="inherited">
          Returns an <a href="dart-core/Iterable-class.html">Iterable</a> that provides all but the first <code>count</code> elements. <a href="dart-collection/SetMixin/skip.html">[...]</a>
                  <div class="features">inherited</div>
</dd>
        <dt id="skipWhile" class="callable inherited">
          <span class="name"><a href="dart-collection/SetMixin/skipWhile.html">skipWhile</a></span><span class="signature">(<wbr><span class="parameter" id="skipWhile-param-test"><span class="type-annotation"><a href="dart-core/bool-class.html">bool</a></span> <span class="parameter-name">test</span>(<span class="parameter" id="param-value"><span class="type-annotation">E</span> <span class="parameter-name">value</span></span>)</span>)
            <span class="returntype parameter">&#8594; <a href="dart-core/Iterable-class.html">Iterable</a><span class="signature">&lt;<wbr><span class="type-parameter">E</span>&gt;</span></span>
          </span>
                  </dt>
        <dd class="inherited">
          Returns an <code>Iterable</code> that skips leading elements while <code>test</code> is satisfied. <a href="dart-collection/SetMixin/skipWhile.html">[...]</a>
                  <div class="features">inherited</div>
</dd>
        <dt id="take" class="callable inherited">
          <span class="name"><a href="dart-collection/SetMixin/take.html">take</a></span><span class="signature">(<wbr><span class="parameter" id="take-param-n"><span class="type-annotation"><a href="dart-core/int-class.html">int</a></span> <span class="parameter-name">n</span></span>)
            <span class="returntype parameter">&#8594; <a href="dart-core/Iterable-class.html">Iterable</a><span class="signature">&lt;<wbr><span class="type-parameter">E</span>&gt;</span></span>
          </span>
                  </dt>
        <dd class="inherited">
          Returns a lazy iterable of the <code>count</code> first elements of this iterable. <a href="dart-collection/SetMixin/take.html">[...]</a>
                  <div class="features">inherited</div>
</dd>
        <dt id="takeWhile" class="callable inherited">
          <span class="name"><a href="dart-collection/SetMixin/takeWhile.html">takeWhile</a></span><span class="signature">(<wbr><span class="parameter" id="takeWhile-param-test"><span class="type-annotation"><a href="dart-core/bool-class.html">bool</a></span> <span class="parameter-name">test</span>(<span class="parameter" id="param-value"><span class="type-annotation">E</span> <span class="parameter-name">value</span></span>)</span>)
            <span class="returntype parameter">&#8594; <a href="dart-core/Iterable-class.html">Iterable</a><span class="signature">&lt;<wbr><span class="type-parameter">E</span>&gt;</span></span>
          </span>
                  </dt>
        <dd class="inherited">
          Returns a lazy iterable of the leading elements satisfying <code>test</code>. <a href="dart-collection/SetMixin/takeWhile.html">[...]</a>
                  <div class="features">inherited</div>
</dd>
        <dt id="toList" class="callable inherited">
          <span class="name"><a href="dart-collection/SetMixin/toList.html">toList</a></span><span class="signature">(<wbr>{<span class="parameter" id="toList-param-growable"><span class="type-annotation"><a href="dart-core/bool-class.html">bool</a></span> <span class="parameter-name">growable</span>: <span class="default-value">true</span></span> })
            <span class="returntype parameter">&#8594; <a href="dart-core/List-class.html">List</a><span class="signature">&lt;<wbr><span class="type-parameter">E</span>&gt;</span></span>
          </span>
                  </dt>
        <dd class="inherited">
          Creates a <a href="dart-core/List-class.html">List</a> containing the elements of this <a href="dart-core/Iterable-class.html">Iterable</a>. <a href="dart-collection/SetMixin/toList.html">[...]</a>
                  <div class="features">inherited</div>
</dd>
        <dt id="where" class="callable inherited">
          <span class="name"><a href="dart-collection/SetMixin/where.html">where</a></span><span class="signature">(<wbr><span class="parameter" id="where-param-f"><span class="type-annotation"><a href="dart-core/bool-class.html">bool</a></span> <span class="parameter-name">f</span>(<span class="parameter" id="param-element"><span class="type-annotation">E</span> <span class="parameter-name">element</span></span>)</span>)
            <span class="returntype parameter">&#8594; <a href="dart-core/Iterable-class.html">Iterable</a><span class="signature">&lt;<wbr><span class="type-parameter">E</span>&gt;</span></span>
          </span>
                  </dt>
        <dd class="inherited">
          Returns a new lazy <a href="dart-core/Iterable-class.html">Iterable</a> with all elements that satisfy the
predicate <code>test</code>. <a href="dart-collection/SetMixin/where.html">[...]</a>
                  <div class="features">inherited</div>
</dd>
        <dt id="whereType" class="callable inherited">
          <span class="name"><a href="dart-collection/SetMixin/whereType.html">whereType</a></span><span class="signature">&lt;<wbr><span class="type-parameter">T</span>&gt;</span><span class="signature">(<wbr>)
            <span class="returntype parameter">&#8594; <a href="dart-core/Iterable-class.html">Iterable</a><span class="signature">&lt;<wbr><span class="type-parameter">T</span>&gt;</span></span>
          </span>
                  </dt>
        <dd class="inherited">
          Returns a new lazy <a href="dart-core/Iterable-class.html">Iterable</a> with all elements that have type <code>T</code>. <a href="dart-collection/SetMixin/whereType.html">[...]</a>
                  <div class="features">inherited</div>
</dd>
      </dl>
    </section>

    <section class="summary offset-anchor inherited" id="operators">
      <h2>Operators</h2>
      <dl class="callables">
        <dt id="operator ==" class="callable inherited">
          <span class="name"><a href="dart-core/Object/operator_equals.html">operator ==</a></span><span class="signature">(<wbr><span class="parameter" id="==-param-other"><span class="type-annotation"><a href="dart-core/Object-class.html">Object</a></span> <span class="parameter-name">other</span></span>)
            <span class="returntype parameter">&#8594; <a href="dart-core/bool-class.html">bool</a></span>
          </span>
                  </dt>
        <dd class="inherited">
          The equality operator. <a href="dart-core/Object/operator_equals.html">[...]</a>
                  <div class="features">inherited</div>
</dd>
      </dl>
    </section>




  </div> <!-- /.main-content -->

  <div id="dartdoc-sidebar-right" class="col-xs-6 col-sm-6 col-md-2 sidebar sidebar-offcanvas-right">
    <ol>
    
      <li class="section-title"><a href="dart-collection/SplayTreeSet-class.html#constructors">Constructors</a></li>
      <li><a href="dart-collection/SplayTreeSet/SplayTreeSet.html">SplayTreeSet</a></li>
      <li><a href="dart-collection/SplayTreeSet/SplayTreeSet.from.html">from</a></li>
      <li><a href="dart-collection/SplayTreeSet/SplayTreeSet.of.html">of</a></li>
    
      <li class="section-title">
        <a href="dart-collection/SplayTreeSet-class.html#instance-properties">Properties</a>
      </li>
      <li><a href="dart-collection/SplayTreeSet/first.html">first</a></li>
      <li><a href="dart-collection/SplayTreeSet/isEmpty.html">isEmpty</a></li>
      <li><a href="dart-collection/SplayTreeSet/isNotEmpty.html">isNotEmpty</a></li>
      <li><a href="dart-collection/SplayTreeSet/iterator.html">iterator</a></li>
      <li><a href="dart-collection/SplayTreeSet/last.html">last</a></li>
      <li><a href="dart-collection/SplayTreeSet/length.html">length</a></li>
      <li><a href="dart-collection/SplayTreeSet/single.html">single</a></li>
      <li class="inherited"><a href="dart-core/Object/hashCode.html">hashCode</a></li>
      <li class="inherited"><a href="dart-core/Object/runtimeType.html">runtimeType</a></li>
    
      <li class="section-title"><a href="dart-collection/SplayTreeSet-class.html#instance-methods">Methods</a></li>
      <li><a href="dart-collection/SplayTreeSet/add.html">add</a></li>
      <li><a href="dart-collection/SplayTreeSet/addAll.html">addAll</a></li>
      <li><a href="dart-collection/SplayTreeSet/cast.html">cast</a></li>
      <li><a href="dart-collection/SplayTreeSet/clear.html">clear</a></li>
      <li><a href="dart-collection/SplayTreeSet/contains.html">contains</a></li>
      <li><a href="dart-collection/SplayTreeSet/difference.html">difference</a></li>
      <li><a href="dart-collection/SplayTreeSet/intersection.html">intersection</a></li>
      <li><a href="dart-collection/SplayTreeSet/lookup.html">lookup</a></li>
      <li><a href="dart-collection/SplayTreeSet/remove.html">remove</a></li>
      <li><a href="dart-collection/SplayTreeSet/removeAll.html">removeAll</a></li>
      <li><a href="dart-collection/SplayTreeSet/retainAll.html">retainAll</a></li>
      <li><a href="dart-collection/SplayTreeSet/toSet.html">toSet</a></li>
      <li><a href="dart-collection/SplayTreeSet/toString.html">toString</a></li>
      <li><a href="dart-collection/SplayTreeSet/union.html">union</a></li>
      <li class="inherited"><a href="dart-collection/SetMixin/any.html">any</a></li>
      <li class="inherited"><a href="dart-collection/SetMixin/containsAll.html">containsAll</a></li>
      <li class="inherited"><a href="dart-collection/SetMixin/elementAt.html">elementAt</a></li>
      <li class="inherited"><a href="dart-collection/SetMixin/every.html">every</a></li>
      <li class="inherited"><a href="dart-collection/SetMixin/expand.html">expand</a></li>
      <li class="inherited"><a href="dart-collection/SetMixin/firstWhere.html">firstWhere</a></li>
      <li class="inherited"><a href="dart-collection/SetMixin/fold.html">fold</a></li>
      <li class="inherited"><a href="dart-collection/SetMixin/followedBy.html">followedBy</a></li>
      <li class="inherited"><a href="dart-collection/SetMixin/forEach.html">forEach</a></li>
      <li class="inherited"><a href="dart-collection/SetMixin/join.html">join</a></li>
      <li class="inherited"><a href="dart-collection/SetMixin/lastWhere.html">lastWhere</a></li>
      <li class="inherited"><a href="dart-collection/SetMixin/map.html">map</a></li>
      <li class="inherited"><a href="dart-core/Object/noSuchMethod.html">noSuchMethod</a></li>
      <li class="inherited"><a href="dart-collection/SetMixin/reduce.html">reduce</a></li>
      <li class="inherited"><a href="dart-collection/SetMixin/removeWhere.html">removeWhere</a></li>
      <li class="inherited"><a href="dart-collection/SetMixin/retainWhere.html">retainWhere</a></li>
      <li class="inherited"><a href="dart-collection/SetMixin/singleWhere.html">singleWhere</a></li>
      <li class="inherited"><a href="dart-collection/SetMixin/skip.html">skip</a></li>
      <li class="inherited"><a href="dart-collection/SetMixin/skipWhile.html">skipWhile</a></li>
      <li class="inherited"><a href="dart-collection/SetMixin/take.html">take</a></li>
      <li class="inherited"><a href="dart-collection/SetMixin/takeWhile.html">takeWhile</a></li>
      <li class="inherited"><a href="dart-collection/SetMixin/toList.html">toList</a></li>
      <li class="inherited"><a href="dart-collection/SetMixin/where.html">where</a></li>
      <li class="inherited"><a href="dart-collection/SetMixin/whereType.html">whereType</a></li>
    
      <li class="section-title inherited"><a href="dart-collection/SplayTreeSet-class.html#operators">Operators</a></li>
      <li class="inherited"><a href="dart-core/Object/operator_equals.html">operator ==</a></li>
    
    
    
    
    </ol>
  </div><!--/.sidebar-offcanvas-->

</main>

<footer>
  <span class="no-break">
    gms_check
      1.0.0
  </span>

  <!-- footer-text placeholder -->
</footer>

<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.2.1/jquery.min.js"></script>
<script src="static-assets/typeahead.bundle.min.js"></script>
<script src="static-assets/highlight.pack.js"></script>
<script src="static-assets/URI.js"></script>
<script src="static-assets/script.js"></script>
<!-- footer placeholder -->

</body>

</html>
