<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, height=device-height, initial-scale=1, user-scalable=no">
  <meta name="description" content="API docs for the LinkedHashMap constructor from the Class LinkedHashMap class from the dart:collection library, for the Dart programming language.">
  <title>LinkedHashMap constructor - LinkedHashMap class - dart:collection library - Dart API</title>
  <!-- required because all the links are pseudo-absolute -->
  <base href="../..">

  <link href="https://fonts.googleapis.com/css?family=Source+Code+Pro:500,400i,400,300|Source+Sans+Pro:400,300,700" rel="stylesheet">
  <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
  <link rel="stylesheet" href="static-assets/github.css">
  <link rel="stylesheet" href="static-assets/styles.css">
  <link rel="icon" href="static-assets/favicon.png">
  <!-- header placeholder -->
</head>

<body>

<div id="overlay-under-drawer"></div>

<header id="title">
  <button id="sidenav-left-toggle" type="button">&nbsp;</button>
  <ol class="breadcrumbs gt-separated dark hidden-xs">
    <li><a href="index.html">gms_check</a></li>
    <li><a href="dart-collection/dart-collection-library.html">dart:collection</a></li>
    <li><a href="dart-collection/LinkedHashMap-class.html">LinkedHashMap<span class="signature">&lt;<wbr><span class="type-parameter">K</span>, <span class="type-parameter">V</span>&gt;</span></a></li>
    <li class="self-crumb">LinkedHashMap factory constructor</li>
  </ol>
  <div class="self-name">LinkedHashMap</div>
  <form class="search navbar-right" role="search">
    <input type="text" id="search-box" autocomplete="off" disabled class="form-control typeahead" placeholder="Loading search...">
  </form>
</header>

<main>

  <div id="dartdoc-sidebar-left" class="col-xs-6 col-sm-3 col-md-2 sidebar sidebar-offcanvas-left">
    <header id="header-search-sidebar" class="hidden-l">
      <form class="search-sidebar" role="search">
        <input type="text" id="search-sidebar" autocomplete="off" disabled class="form-control typeahead" placeholder="Loading search...">
      </form>
    </header>
    
    <ol class="breadcrumbs gt-separated dark hidden-l" id="sidebar-nav">
      <li><a href="index.html">gms_check</a></li>
      <li><a href="dart-collection/dart-collection-library.html">dart:collection</a></li>
      <li><a href="dart-collection/LinkedHashMap-class.html">LinkedHashMap<span class="signature">&lt;<wbr><span class="type-parameter">K</span>, <span class="type-parameter">V</span>&gt;</span></a></li>
      <li class="self-crumb">LinkedHashMap factory constructor</li>
    </ol>
    
    <h5>LinkedHashMap class</h5>
    <ol>
    
      <li class="section-title"><a href="dart-collection/LinkedHashMap-class.html#constructors">Constructors</a></li>
      <li><a href="dart-collection/LinkedHashMap/LinkedHashMap.html">LinkedHashMap</a></li>
      <li><a href="dart-collection/LinkedHashMap/LinkedHashMap.from.html">from</a></li>
      <li><a href="dart-collection/LinkedHashMap/LinkedHashMap.fromEntries.html">fromEntries</a></li>
      <li><a href="dart-collection/LinkedHashMap/LinkedHashMap.fromIterable.html">fromIterable</a></li>
      <li><a href="dart-collection/LinkedHashMap/LinkedHashMap.fromIterables.html">fromIterables</a></li>
      <li><a href="dart-collection/LinkedHashMap/LinkedHashMap.identity.html">identity</a></li>
      <li><a href="dart-collection/LinkedHashMap/LinkedHashMap.of.html">of</a></li>
    
      <li class="section-title inherited">
        <a href="dart-collection/LinkedHashMap-class.html#instance-properties">Properties</a>
      </li>
      <li class="inherited"><a href="dart-core/Map/entries.html">entries</a></li>
      <li class="inherited"><a href="dart-core/Object/hashCode.html">hashCode</a></li>
      <li class="inherited"><a href="dart-core/Map/isEmpty.html">isEmpty</a></li>
      <li class="inherited"><a href="dart-core/Map/isNotEmpty.html">isNotEmpty</a></li>
      <li class="inherited"><a href="dart-core/Map/keys.html">keys</a></li>
      <li class="inherited"><a href="dart-core/Map/length.html">length</a></li>
      <li class="inherited"><a href="dart-core/Object/runtimeType.html">runtimeType</a></li>
      <li class="inherited"><a href="dart-core/Map/values.html">values</a></li>
    
      <li class="section-title inherited"><a href="dart-collection/LinkedHashMap-class.html#instance-methods">Methods</a></li>
      <li class="inherited"><a href="dart-core/Map/addAll.html">addAll</a></li>
      <li class="inherited"><a href="dart-core/Map/addEntries.html">addEntries</a></li>
      <li class="inherited"><a href="dart-core/Map/cast.html">cast</a></li>
      <li class="inherited"><a href="dart-core/Map/clear.html">clear</a></li>
      <li class="inherited"><a href="dart-core/Map/containsKey.html">containsKey</a></li>
      <li class="inherited"><a href="dart-core/Map/containsValue.html">containsValue</a></li>
      <li class="inherited"><a href="dart-core/Map/forEach.html">forEach</a></li>
      <li class="inherited"><a href="dart-core/Map/map.html">map</a></li>
      <li class="inherited"><a href="dart-core/Object/noSuchMethod.html">noSuchMethod</a></li>
      <li class="inherited"><a href="dart-core/Map/putIfAbsent.html">putIfAbsent</a></li>
      <li class="inherited"><a href="dart-core/Map/remove.html">remove</a></li>
      <li class="inherited"><a href="dart-core/Map/removeWhere.html">removeWhere</a></li>
      <li class="inherited"><a href="dart-core/Object/toString.html">toString</a></li>
      <li class="inherited"><a href="dart-core/Map/update.html">update</a></li>
      <li class="inherited"><a href="dart-core/Map/updateAll.html">updateAll</a></li>
    
      <li class="section-title inherited"><a href="dart-collection/LinkedHashMap-class.html#operators">Operators</a></li>
      <li class="inherited"><a href="dart-core/Object/operator_equals.html">operator ==</a></li>
      <li class="inherited"><a href="dart-core/Map/operator_get.html">operator []</a></li>
      <li class="inherited"><a href="dart-core/Map/operator_put.html">operator []=</a></li>
    
    
    
    
    </ol>
  </div><!--/.sidebar-offcanvas-left-->

  <div id="dartdoc-main-content" class="col-xs-12 col-sm-9 col-md-8 main-content">
      <div><h1><span class="kind-constructor">LinkedHashMap&lt;<wbr><span class="type-parameter">K</span>, <span class="type-parameter">V</span>&gt;</span> constructor</h1></div>

    <section class="multi-line-signature">
      
      <span class="name ">LinkedHashMap&lt;<wbr><span class="type-parameter">K</span>, <span class="type-parameter">V</span>&gt;</span>(<wbr>{<span class="parameter" id="-param-equals"><span class="type-annotation"><a href="dart-core/bool-class.html">bool</a></span> <span class="parameter-name">equals</span>(<span class="parameter" id="param-"><span class="type-annotation">K</span></span> <span class="parameter" id="param-"><span class="type-annotation">K</span></span>), </span> <span class="parameter" id="-param-hashCode"><span class="type-annotation"><a href="dart-core/int-class.html">int</a></span> <span class="parameter-name">hashCode</span>(<span class="parameter" id="param-"><span class="type-annotation">K</span></span>), </span> <span class="parameter" id="-param-isValidKey"><span class="type-annotation"><a href="dart-core/bool-class.html">bool</a></span> <span class="parameter-name">isValidKey</span>(<span class="parameter" id="param-"><span class="type-annotation">dynamic</span></span>)</span> })
    </section>

    <section class="desc markdown">
      <p>Creates an insertion-ordered hash-table based <a href="dart-core/Map-class.html">Map</a>.</p>
<p>If <code>equals</code> is provided, it is used to compare the keys in the table with
new keys. If <code>equals</code> is omitted, the key's own <a href="dart-core/Object/operator_equals.html">Object.==</a> is used
instead.</p>
<p>Similar, if <code>hashCode</code> is provided, it is used to produce a hash value
for keys in order to place them in the hash table. If it is omitted, the
key's own <a href="dart-core/Object/hashCode.html">Object.hashCode</a> is used.</p>
<p>If using methods like <a href="dart-core/Map/operator_get.html">operator []</a>, <a href="dart-core/Map/remove.html">remove</a> and <a href="dart-core/Map/containsKey.html">containsKey</a> together
with a custom equality and hashcode, an extra <code>isValidKey</code> function
can be supplied. This function is called before calling <code>equals</code> or
<code>hashCode</code> with an argument that may not be a <code>K</code> instance, and if the
call returns false, the key is assumed to not be in the set.
The <code>isValidKey</code> function defaults to just testing if the object is a
<code>K</code> instance.</p>
<p>Example:</p>
<pre class="language-dart"><code class="language-dart">LinkedHashMap&lt;int,int&gt;(equals: (int a, int b) =&gt; (b - a) % 5 == 0,
                       hashCode: (int e) =&gt; e % 5)
</code></pre>
<p>This example map does not need an <code>isValidKey</code> function to be passed.
The default function accepts only <code>int</code> values, which can safely be
passed to both the <code>equals</code> and <code>hashCode</code> functions.</p>
<p>If neither <code>equals</code>, <code>hashCode</code>, nor <code>isValidKey</code> is provided,
the default <code>isValidKey</code> instead accepts all keys.
The default equality and hashcode operations are assumed to work on all
objects.</p>
<p>Likewise, if <code>equals</code> is <a href="dart-core/identical.html">identical</a>, <code>hashCode</code> is <a href="dart-core/identityHashCode.html">identityHashCode</a>
and <code>isValidKey</code> is omitted, the resulting map is identity based,
and the <code>isValidKey</code> defaults to accepting all keys.
Such a map can be created directly using <a href="dart-collection/LinkedHashMap/LinkedHashMap.identity.html">LinkedHashMap.identity</a>.</p>
<p>The used <code>equals</code> and <code>hashCode</code> method should always be consistent,
so that if <code>equals(a, b)</code> then <code>hashCode(a) == hashCode(b)</code>. The hash
of an object, or what it compares equal to, should not change while the
object is in the table. If it does change, the result is unpredictable.</p>
<p>If you supply one of <code>equals</code> and <code>hashCode</code>,
you should generally also to supply the other.</p>
    </section>
    
    <section class="summary source-code" id="source">
      <h2><span>Implementation</span></h2>
      <pre class="language-dart"><code class="language-dart">external factory LinkedHashMap(
    {bool Function(K, K)? equals,
    int Function(K)? hashCode,
    bool Function(dynamic)? isValidKey});</code></pre>
    </section>

  </div> <!-- /.main-content -->

  <div id="dartdoc-sidebar-right" class="col-xs-6 col-sm-6 col-md-2 sidebar sidebar-offcanvas-right">
  </div><!--/.sidebar-offcanvas-->

</main>

<footer>
  <span class="no-break">
    gms_check
      1.0.0
  </span>

  <!-- footer-text placeholder -->
</footer>

<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.2.1/jquery.min.js"></script>
<script src="static-assets/typeahead.bundle.min.js"></script>
<script src="static-assets/highlight.pack.js"></script>
<script src="static-assets/URI.js"></script>
<script src="static-assets/script.js"></script>
<!-- footer placeholder -->

</body>

</html>
