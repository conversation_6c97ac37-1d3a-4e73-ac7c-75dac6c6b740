<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, height=device-height, initial-scale=1, user-scalable=no">
  <meta name="description" content="API docs for the LinkedListEntry class from the dart:collection library, for the Dart programming language.">
  <title>LinkedListEntry class - dart:collection library - Dart API</title>
  <!-- required because all the links are pseudo-absolute -->
  <base href="..">

  <link href="https://fonts.googleapis.com/css?family=Source+Code+Pro:500,400i,400,300|Source+Sans+Pro:400,300,700" rel="stylesheet">
  <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
  <link rel="stylesheet" href="static-assets/github.css">
  <link rel="stylesheet" href="static-assets/styles.css">
  <link rel="icon" href="static-assets/favicon.png">
  <!-- header placeholder -->
</head>

<body>

<div id="overlay-under-drawer"></div>

<header id="title">
  <button id="sidenav-left-toggle" type="button">&nbsp;</button>
  <ol class="breadcrumbs gt-separated dark hidden-xs">
    <li><a href="index.html">gms_check</a></li>
    <li><a href="dart-collection/dart-collection-library.html">dart:collection</a></li>
    <li class="self-crumb">LinkedListEntry<span class="signature">&lt;<wbr><span class="type-parameter">E extends <a href="dart-collection/LinkedListEntry-class.html">LinkedListEntry</a><span class="signature">&lt;<wbr><span class="type-parameter">E</span>&gt;</span></span>&gt;</span> abstract class</li>
  </ol>
  <div class="self-name">LinkedListEntry</div>
  <form class="search navbar-right" role="search">
    <input type="text" id="search-box" autocomplete="off" disabled class="form-control typeahead" placeholder="Loading search...">
  </form>
</header>

<main>

  <div id="dartdoc-sidebar-left" class="col-xs-6 col-sm-3 col-md-2 sidebar sidebar-offcanvas-left">
    <header id="header-search-sidebar" class="hidden-l">
      <form class="search-sidebar" role="search">
        <input type="text" id="search-sidebar" autocomplete="off" disabled class="form-control typeahead" placeholder="Loading search...">
      </form>
    </header>
    
    <ol class="breadcrumbs gt-separated dark hidden-l" id="sidebar-nav">
      <li><a href="index.html">gms_check</a></li>
      <li><a href="dart-collection/dart-collection-library.html">dart:collection</a></li>
      <li class="self-crumb">LinkedListEntry<span class="signature">&lt;<wbr><span class="type-parameter">E extends <a href="dart-collection/LinkedListEntry-class.html">LinkedListEntry</a><span class="signature">&lt;<wbr><span class="type-parameter">E</span>&gt;</span></span>&gt;</span> abstract class</li>
    </ol>
    
    <h5>dart:collection library</h5>
    <ol>
      <li class="section-title"><a href="dart-collection/dart-collection-library.html#classes">Classes</a></li>
      <li><a href="dart-collection/DoubleLinkedQueue-class.html">DoubleLinkedQueue</a></li>
      <li><a href="dart-collection/DoubleLinkedQueueEntry-class.html">DoubleLinkedQueueEntry</a></li>
      <li><a href="dart-collection/HashMap-class.html">HashMap</a></li>
      <li><a href="dart-collection/HashSet-class.html">HashSet</a></li>
      <li><a href="dart-collection/HasNextIterator-class.html">HasNextIterator</a></li>
      <li><a href="dart-collection/IterableBase-class.html">IterableBase</a></li>
      <li><a href="dart-collection/IterableMixin-class.html">IterableMixin</a></li>
      <li><a href="dart-collection/LinkedHashMap-class.html">LinkedHashMap</a></li>
      <li><a href="dart-collection/LinkedHashSet-class.html">LinkedHashSet</a></li>
      <li><a href="dart-collection/LinkedList-class.html">LinkedList</a></li>
      <li><a href="dart-collection/LinkedListEntry-class.html">LinkedListEntry</a></li>
      <li><a href="dart-collection/ListBase-class.html">ListBase</a></li>
      <li><a href="dart-collection/ListMixin-class.html">ListMixin</a></li>
      <li><a href="dart-collection/ListQueue-class.html">ListQueue</a></li>
      <li><a href="dart-collection/MapBase-class.html">MapBase</a></li>
      <li><a href="dart-collection/MapMixin-class.html">MapMixin</a></li>
      <li><a href="dart-collection/MapView-class.html">MapView</a></li>
      <li><a href="dart-collection/Queue-class.html">Queue</a></li>
      <li><a href="dart-collection/SetBase-class.html">SetBase</a></li>
      <li><a href="dart-collection/SetMixin-class.html">SetMixin</a></li>
      <li><a href="dart-collection/SplayTreeMap-class.html">SplayTreeMap</a></li>
      <li><a href="dart-collection/SplayTreeSet-class.html">SplayTreeSet</a></li>
      <li><a href="dart-collection/UnmodifiableListView-class.html">UnmodifiableListView</a></li>
      <li><a href="dart-collection/UnmodifiableMapBase-class.html">UnmodifiableMapBase</a></li>
      <li><a href="dart-collection/UnmodifiableMapView-class.html">UnmodifiableMapView</a></li>
      <li><a href="dart-collection/UnmodifiableSetView-class.html">UnmodifiableSetView</a></li>
    
    
    
    
    
    
    
    
    </ol>
  </div>

  <div id="dartdoc-main-content" class="col-xs-12 col-sm-9 col-md-8 main-content">
      <div><h1><span class="kind-class">LinkedListEntry&lt;<wbr><span class="type-parameter">E extends LinkedListEntry&lt;<wbr><span class="type-parameter">E</span>&gt;</span>&gt;</span> class </h1></div>

    <section class="desc markdown">
      <p>An object that can be an element in a <a href="dart-collection/LinkedList-class.html">LinkedList</a>.</p>
<p>All elements of a <code>LinkedList</code> must extend this class.
The class provides the internal links that link elements together
in the <code>LinkedList</code>, and a reference to the linked list itself
that an element is currently part of.</p>
<p>An entry can be in at most one linked list at a time.
While an entry is in a linked list, the <a href="dart-collection/LinkedListEntry/list.html">list</a> property points to that
linked list, and otherwise the <code>list</code> property is <code>null</code>.</p>
<p>When created, an entry is not in any linked list.</p>
    </section>
    

    <section class="summary offset-anchor" id="constructors">
      <h2>Constructors</h2>

      <dl class="constructor-summary-list">
        <dt id="LinkedListEntry" class="callable">
          <span class="name"><a href="dart-collection/LinkedListEntry/LinkedListEntry.html">LinkedListEntry</a></span><span class="signature">()</span>
        </dt>
        <dd>
          
        </dd>
      </dl>
    </section>

    <section class="summary offset-anchor" id="instance-properties">
      <h2>Properties</h2>

      <dl class="properties">
        <dt id="list" class="property">
          <span class="name"><a href="dart-collection/LinkedListEntry/list.html">list</a></span>
          <span class="signature">&#8594; <a href="dart-collection/LinkedList-class.html">LinkedList</a><span class="signature">&lt;<wbr><span class="type-parameter">E</span>&gt;</span></span>         
        </dt>
        <dd>
          The linked list containing this element. <a href="dart-collection/LinkedListEntry/list.html">[...]</a>
                  <div class="features">read-only</div>
</dd>
        <dt id="next" class="property">
          <span class="name"><a href="dart-collection/LinkedListEntry/next.html">next</a></span>
          <span class="signature">&#8594; E</span>         
        </dt>
        <dd>
          The successor of this element in its linked list. <a href="dart-collection/LinkedListEntry/next.html">[...]</a>
                  <div class="features">read-only</div>
</dd>
        <dt id="previous" class="property">
          <span class="name"><a href="dart-collection/LinkedListEntry/previous.html">previous</a></span>
          <span class="signature">&#8594; E</span>         
        </dt>
        <dd>
          The predecessor of this element in its linked list. <a href="dart-collection/LinkedListEntry/previous.html">[...]</a>
                  <div class="features">read-only</div>
</dd>
        <dt id="hashCode" class="property inherited">
          <span class="name"><a href="dart-core/Object/hashCode.html">hashCode</a></span>
          <span class="signature">&#8594; <a href="dart-core/int-class.html">int</a></span>         
        </dt>
        <dd class="inherited">
          The hash code for this object. <a href="dart-core/Object/hashCode.html">[...]</a>
                  <div class="features">read-only, inherited</div>
</dd>
        <dt id="runtimeType" class="property inherited">
          <span class="name"><a href="dart-core/Object/runtimeType.html">runtimeType</a></span>
          <span class="signature">&#8594; <a href="dart-core/Type-class.html">Type</a></span>         
        </dt>
        <dd class="inherited">
          A representation of the runtime type of the object.
                  <div class="features">read-only, inherited</div>
</dd>
      </dl>
    </section>

    <section class="summary offset-anchor" id="instance-methods">
      <h2>Methods</h2>
      <dl class="callables">
        <dt id="insertAfter" class="callable">
          <span class="name"><a href="dart-collection/LinkedListEntry/insertAfter.html">insertAfter</a></span><span class="signature">(<wbr><span class="parameter" id="insertAfter-param-entry"><span class="type-annotation">E</span> <span class="parameter-name">entry</span></span>)
            <span class="returntype parameter">&#8594; void</span>
          </span>
                  </dt>
        <dd>
          Insert an element after this element in this element's linked list. <a href="dart-collection/LinkedListEntry/insertAfter.html">[...]</a>
                  
</dd>
        <dt id="insertBefore" class="callable">
          <span class="name"><a href="dart-collection/LinkedListEntry/insertBefore.html">insertBefore</a></span><span class="signature">(<wbr><span class="parameter" id="insertBefore-param-entry"><span class="type-annotation">E</span> <span class="parameter-name">entry</span></span>)
            <span class="returntype parameter">&#8594; void</span>
          </span>
                  </dt>
        <dd>
          Insert an element before this element in this element's linked list. <a href="dart-collection/LinkedListEntry/insertBefore.html">[...]</a>
                  
</dd>
        <dt id="unlink" class="callable">
          <span class="name"><a href="dart-collection/LinkedListEntry/unlink.html">unlink</a></span><span class="signature">(<wbr>)
            <span class="returntype parameter">&#8594; void</span>
          </span>
                  </dt>
        <dd>
          Unlink the element from its linked list. <a href="dart-collection/LinkedListEntry/unlink.html">[...]</a>
                  
</dd>
        <dt id="noSuchMethod" class="callable inherited">
          <span class="name"><a href="dart-core/Object/noSuchMethod.html">noSuchMethod</a></span><span class="signature">(<wbr><span class="parameter" id="noSuchMethod-param-invocation"><span class="type-annotation"><a href="dart-core/Invocation-class.html">Invocation</a></span> <span class="parameter-name">invocation</span></span>)
            <span class="returntype parameter">&#8594; dynamic</span>
          </span>
                  </dt>
        <dd class="inherited">
          Invoked when a non-existent method or property is accessed. <a href="dart-core/Object/noSuchMethod.html">[...]</a>
                  <div class="features">inherited</div>
</dd>
        <dt id="toString" class="callable inherited">
          <span class="name"><a href="dart-core/Object/toString.html">toString</a></span><span class="signature">(<wbr>)
            <span class="returntype parameter">&#8594; <a href="dart-core/String-class.html">String</a></span>
          </span>
                  </dt>
        <dd class="inherited">
          A string representation of this object. <a href="dart-core/Object/toString.html">[...]</a>
                  <div class="features">inherited</div>
</dd>
      </dl>
    </section>

    <section class="summary offset-anchor inherited" id="operators">
      <h2>Operators</h2>
      <dl class="callables">
        <dt id="operator ==" class="callable inherited">
          <span class="name"><a href="dart-core/Object/operator_equals.html">operator ==</a></span><span class="signature">(<wbr><span class="parameter" id="==-param-other"><span class="type-annotation"><a href="dart-core/Object-class.html">Object</a></span> <span class="parameter-name">other</span></span>)
            <span class="returntype parameter">&#8594; <a href="dart-core/bool-class.html">bool</a></span>
          </span>
                  </dt>
        <dd class="inherited">
          The equality operator. <a href="dart-core/Object/operator_equals.html">[...]</a>
                  <div class="features">inherited</div>
</dd>
      </dl>
    </section>




  </div> <!-- /.main-content -->

  <div id="dartdoc-sidebar-right" class="col-xs-6 col-sm-6 col-md-2 sidebar sidebar-offcanvas-right">
    <ol>
    
      <li class="section-title"><a href="dart-collection/LinkedListEntry-class.html#constructors">Constructors</a></li>
      <li><a href="dart-collection/LinkedListEntry/LinkedListEntry.html">LinkedListEntry</a></li>
    
      <li class="section-title">
        <a href="dart-collection/LinkedListEntry-class.html#instance-properties">Properties</a>
      </li>
      <li><a href="dart-collection/LinkedListEntry/list.html">list</a></li>
      <li><a href="dart-collection/LinkedListEntry/next.html">next</a></li>
      <li><a href="dart-collection/LinkedListEntry/previous.html">previous</a></li>
      <li class="inherited"><a href="dart-core/Object/hashCode.html">hashCode</a></li>
      <li class="inherited"><a href="dart-core/Object/runtimeType.html">runtimeType</a></li>
    
      <li class="section-title"><a href="dart-collection/LinkedListEntry-class.html#instance-methods">Methods</a></li>
      <li><a href="dart-collection/LinkedListEntry/insertAfter.html">insertAfter</a></li>
      <li><a href="dart-collection/LinkedListEntry/insertBefore.html">insertBefore</a></li>
      <li><a href="dart-collection/LinkedListEntry/unlink.html">unlink</a></li>
      <li class="inherited"><a href="dart-core/Object/noSuchMethod.html">noSuchMethod</a></li>
      <li class="inherited"><a href="dart-core/Object/toString.html">toString</a></li>
    
      <li class="section-title inherited"><a href="dart-collection/LinkedListEntry-class.html#operators">Operators</a></li>
      <li class="inherited"><a href="dart-core/Object/operator_equals.html">operator ==</a></li>
    
    
    
    
    </ol>
  </div><!--/.sidebar-offcanvas-->

</main>

<footer>
  <span class="no-break">
    gms_check
      1.0.0
  </span>

  <!-- footer-text placeholder -->
</footer>

<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.2.1/jquery.min.js"></script>
<script src="static-assets/typeahead.bundle.min.js"></script>
<script src="static-assets/highlight.pack.js"></script>
<script src="static-assets/URI.js"></script>
<script src="static-assets/script.js"></script>
<!-- footer placeholder -->

</body>

</html>
