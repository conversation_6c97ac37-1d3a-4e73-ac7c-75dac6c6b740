<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, height=device-height, initial-scale=1, user-scalable=no">
  <meta name="description" content="API docs for the cast method from the MapView class, for the Dart programming language.">
  <title>cast method - MapView class - dart:collection library - Dart API</title>
  <!-- required because all the links are pseudo-absolute -->
  <base href="../..">

  <link href="https://fonts.googleapis.com/css?family=Source+Code+Pro:500,400i,400,300|Source+Sans+Pro:400,300,700" rel="stylesheet">
  <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
  <link rel="stylesheet" href="static-assets/github.css">
  <link rel="stylesheet" href="static-assets/styles.css">
  <link rel="icon" href="static-assets/favicon.png">
  <!-- header placeholder -->
</head>

<body>

<div id="overlay-under-drawer"></div>

<header id="title">
  <button id="sidenav-left-toggle" type="button">&nbsp;</button>
  <ol class="breadcrumbs gt-separated dark hidden-xs">
    <li><a href="index.html">gms_check</a></li>
    <li><a href="dart-collection/dart-collection-library.html">dart:collection</a></li>
    <li><a href="dart-collection/MapView-class.html">MapView<span class="signature">&lt;<wbr><span class="type-parameter">K</span>, <span class="type-parameter">V</span>&gt;</span></a></li>
    <li class="self-crumb">cast&lt;<wbr><span class="type-parameter">RK</span>, <span class="type-parameter">RV</span>&gt; method</li>
  </ol>
  <div class="self-name">cast</div>
  <form class="search navbar-right" role="search">
    <input type="text" id="search-box" autocomplete="off" disabled class="form-control typeahead" placeholder="Loading search...">
  </form>
</header>

<main>

  <div id="dartdoc-sidebar-left" class="col-xs-6 col-sm-3 col-md-2 sidebar sidebar-offcanvas-left">
    <header id="header-search-sidebar" class="hidden-l">
      <form class="search-sidebar" role="search">
        <input type="text" id="search-sidebar" autocomplete="off" disabled class="form-control typeahead" placeholder="Loading search...">
      </form>
    </header>
    
    <ol class="breadcrumbs gt-separated dark hidden-l" id="sidebar-nav">
      <li><a href="index.html">gms_check</a></li>
      <li><a href="dart-collection/dart-collection-library.html">dart:collection</a></li>
      <li><a href="dart-collection/MapView-class.html">MapView<span class="signature">&lt;<wbr><span class="type-parameter">K</span>, <span class="type-parameter">V</span>&gt;</span></a></li>
      <li class="self-crumb">cast&lt;<wbr><span class="type-parameter">RK</span>, <span class="type-parameter">RV</span>&gt; method</li>
    </ol>
    
    <h5>MapView class</h5>
    <ol>
    
        <li class="section-title"><a href="dart-collection/MapView-class.html#constructors">Constructors</a></li>
        <li><a href="dart-collection/MapView/MapView.html">MapView</a></li>
    
        <li class="section-title">
            <a href="dart-collection/MapView-class.html#instance-properties">Properties</a>
        </li>
        <li><a href="dart-collection/MapView/entries.html">entries</a></li>
        <li><a href="dart-collection/MapView/isEmpty.html">isEmpty</a></li>
        <li><a href="dart-collection/MapView/isNotEmpty.html">isNotEmpty</a></li>
        <li><a href="dart-collection/MapView/keys.html">keys</a></li>
        <li><a href="dart-collection/MapView/length.html">length</a></li>
        <li><a href="dart-collection/MapView/values.html">values</a></li>
        <li class="inherited"><a href="dart-core/Object/hashCode.html">hashCode</a></li>
        <li class="inherited"><a href="dart-core/Object/runtimeType.html">runtimeType</a></li>
    
        <li class="section-title"><a href="dart-collection/MapView-class.html#instance-methods">Methods</a></li>
        <li><a href="dart-collection/MapView/addAll.html">addAll</a></li>
        <li><a href="dart-collection/MapView/addEntries.html">addEntries</a></li>
        <li><a href="dart-collection/MapView/cast.html">cast</a></li>
        <li><a href="dart-collection/MapView/clear.html">clear</a></li>
        <li><a href="dart-collection/MapView/containsKey.html">containsKey</a></li>
        <li><a href="dart-collection/MapView/containsValue.html">containsValue</a></li>
        <li><a href="dart-collection/MapView/forEach.html">forEach</a></li>
        <li><a href="dart-collection/MapView/map.html">map</a></li>
        <li><a href="dart-collection/MapView/putIfAbsent.html">putIfAbsent</a></li>
        <li><a href="dart-collection/MapView/remove.html">remove</a></li>
        <li><a href="dart-collection/MapView/removeWhere.html">removeWhere</a></li>
        <li><a href="dart-collection/MapView/toString.html">toString</a></li>
        <li><a href="dart-collection/MapView/update.html">update</a></li>
        <li><a href="dart-collection/MapView/updateAll.html">updateAll</a></li>
        <li class="inherited"><a href="dart-core/Object/noSuchMethod.html">noSuchMethod</a></li>
    
        <li class="section-title"><a href="dart-collection/MapView-class.html#operators">Operators</a></li>
        <li><a href="dart-collection/MapView/operator_get.html">operator []</a></li>
        <li><a href="dart-collection/MapView/operator_put.html">operator []=</a></li>
        <li class="inherited"><a href="dart-core/Object/operator_equals.html">operator ==</a></li>
    
    
    
    
    
    </ol>
  </div><!--/.sidebar-offcanvas-->

  <div id="dartdoc-main-content" class="col-xs-12 col-sm-9 col-md-8 main-content">
      <div><h1><span class="kind-method">cast&lt;<wbr><span class="type-parameter">RK</span>, <span class="type-parameter">RV</span>&gt;</span> method</h1></div>

    <section class="multi-line-signature">
      <span class="returntype"><a href="dart-core/Map-class.html">Map</a><span class="signature">&lt;<wbr><span class="type-parameter">RK</span>, <span class="type-parameter">RV</span>&gt;</span></span>
            <span class="name ">cast</span>
&lt;<wbr><span class="type-parameter">RK</span>, <span class="type-parameter">RV</span>&gt;(<wbr>)
      <div class="features">override</div>
    </section>
    <section class="desc markdown">
      <p>Provides a view of this map as having <code>RK</code> keys and <code>RV</code> instances,
if necessary.</p>
<p>If this map is already a <code>Map&lt;RK, RV&gt;</code>, it is returned unchanged.</p>
<p>If this set contains only keys of type <code>RK</code> and values of type <code>RV</code>,
all read operations will work correctly.
If any operation exposes a non-<code>RK</code> key or non-<code>RV</code> value,
the operation will throw instead.</p>
<p>Entries added to the map must be valid for both a <code>Map&lt;K, V&gt;</code> and a
<code>Map&lt;RK, RV&gt;</code>.</p>
<p>Methods like <a href="dart-collection/MapView/containsKey.html">containsKey</a>, <a href="dart-collection/MapView/remove.html">remove</a> and <a href="dart-collection/MapView/operator_get.html">operator[]</a>
which accept <code>Object?</code> as argument
will pass the argument directly to the this map's method
without any checks.
That means that you can do <code>mapWithStringKeys.cast&lt;int,int&gt;().remove("a")</code>
successfully, even if it looks like it shouldn't have any effect.</p>
    </section>
    
    <section class="summary source-code" id="source">
      <h2><span>Implementation</span></h2>
      <pre class="language-dart"><code class="language-dart">Map&lt;RK, RV&gt; cast&lt;RK, RV&gt;() =&gt; _map.cast&lt;RK, RV&gt;();</code></pre>
    </section>

  </div> <!-- /.main-content -->

  <div id="dartdoc-sidebar-right" class="col-xs-6 col-sm-6 col-md-2 sidebar sidebar-offcanvas-right">
  </div><!--/.sidebar-offcanvas-->

</main>

<footer>
  <span class="no-break">
    gms_check
      1.0.0
  </span>

  <!-- footer-text placeholder -->
</footer>

<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.2.1/jquery.min.js"></script>
<script src="static-assets/typeahead.bundle.min.js"></script>
<script src="static-assets/highlight.pack.js"></script>
<script src="static-assets/URI.js"></script>
<script src="static-assets/script.js"></script>
<!-- footer placeholder -->

</body>

</html>
