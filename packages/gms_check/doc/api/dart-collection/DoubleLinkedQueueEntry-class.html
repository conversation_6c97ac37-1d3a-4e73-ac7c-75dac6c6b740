<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, height=device-height, initial-scale=1, user-scalable=no">
  <meta name="description" content="API docs for the DoubleLinkedQueueEntry class from the dart:collection library, for the Dart programming language.">
  <title>DoubleLinkedQueueEntry class - dart:collection library - Dart API</title>
  <!-- required because all the links are pseudo-absolute -->
  <base href="..">

  <link href="https://fonts.googleapis.com/css?family=Source+Code+Pro:500,400i,400,300|Source+Sans+Pro:400,300,700" rel="stylesheet">
  <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
  <link rel="stylesheet" href="static-assets/github.css">
  <link rel="stylesheet" href="static-assets/styles.css">
  <link rel="icon" href="static-assets/favicon.png">
  <!-- header placeholder -->
</head>

<body>

<div id="overlay-under-drawer"></div>

<header id="title">
  <button id="sidenav-left-toggle" type="button">&nbsp;</button>
  <ol class="breadcrumbs gt-separated dark hidden-xs">
    <li><a href="index.html">gms_check</a></li>
    <li><a href="dart-collection/dart-collection-library.html">dart:collection</a></li>
    <li class="self-crumb">DoubleLinkedQueueEntry<span class="signature">&lt;<wbr><span class="type-parameter">E</span>&gt;</span> class</li>
  </ol>
  <div class="self-name">DoubleLinkedQueueEntry</div>
  <form class="search navbar-right" role="search">
    <input type="text" id="search-box" autocomplete="off" disabled class="form-control typeahead" placeholder="Loading search...">
  </form>
</header>

<main>

  <div id="dartdoc-sidebar-left" class="col-xs-6 col-sm-3 col-md-2 sidebar sidebar-offcanvas-left">
    <header id="header-search-sidebar" class="hidden-l">
      <form class="search-sidebar" role="search">
        <input type="text" id="search-sidebar" autocomplete="off" disabled class="form-control typeahead" placeholder="Loading search...">
      </form>
    </header>
    
    <ol class="breadcrumbs gt-separated dark hidden-l" id="sidebar-nav">
      <li><a href="index.html">gms_check</a></li>
      <li><a href="dart-collection/dart-collection-library.html">dart:collection</a></li>
      <li class="self-crumb">DoubleLinkedQueueEntry<span class="signature">&lt;<wbr><span class="type-parameter">E</span>&gt;</span> class</li>
    </ol>
    
    <h5>dart:collection library</h5>
    <ol>
      <li class="section-title"><a href="dart-collection/dart-collection-library.html#classes">Classes</a></li>
      <li><a href="dart-collection/DoubleLinkedQueue-class.html">DoubleLinkedQueue</a></li>
      <li><a href="dart-collection/DoubleLinkedQueueEntry-class.html">DoubleLinkedQueueEntry</a></li>
      <li><a href="dart-collection/HashMap-class.html">HashMap</a></li>
      <li><a href="dart-collection/HashSet-class.html">HashSet</a></li>
      <li><a href="dart-collection/HasNextIterator-class.html">HasNextIterator</a></li>
      <li><a href="dart-collection/IterableBase-class.html">IterableBase</a></li>
      <li><a href="dart-collection/IterableMixin-class.html">IterableMixin</a></li>
      <li><a href="dart-collection/LinkedHashMap-class.html">LinkedHashMap</a></li>
      <li><a href="dart-collection/LinkedHashSet-class.html">LinkedHashSet</a></li>
      <li><a href="dart-collection/LinkedList-class.html">LinkedList</a></li>
      <li><a href="dart-collection/LinkedListEntry-class.html">LinkedListEntry</a></li>
      <li><a href="dart-collection/ListBase-class.html">ListBase</a></li>
      <li><a href="dart-collection/ListMixin-class.html">ListMixin</a></li>
      <li><a href="dart-collection/ListQueue-class.html">ListQueue</a></li>
      <li><a href="dart-collection/MapBase-class.html">MapBase</a></li>
      <li><a href="dart-collection/MapMixin-class.html">MapMixin</a></li>
      <li><a href="dart-collection/MapView-class.html">MapView</a></li>
      <li><a href="dart-collection/Queue-class.html">Queue</a></li>
      <li><a href="dart-collection/SetBase-class.html">SetBase</a></li>
      <li><a href="dart-collection/SetMixin-class.html">SetMixin</a></li>
      <li><a href="dart-collection/SplayTreeMap-class.html">SplayTreeMap</a></li>
      <li><a href="dart-collection/SplayTreeSet-class.html">SplayTreeSet</a></li>
      <li><a href="dart-collection/UnmodifiableListView-class.html">UnmodifiableListView</a></li>
      <li><a href="dart-collection/UnmodifiableMapBase-class.html">UnmodifiableMapBase</a></li>
      <li><a href="dart-collection/UnmodifiableMapView-class.html">UnmodifiableMapView</a></li>
      <li><a href="dart-collection/UnmodifiableSetView-class.html">UnmodifiableSetView</a></li>
    
    
    
    
    
    
    
    
    </ol>
  </div>

  <div id="dartdoc-main-content" class="col-xs-12 col-sm-9 col-md-8 main-content">
      <div><h1><span class="kind-class">DoubleLinkedQueueEntry&lt;<wbr><span class="type-parameter">E</span>&gt;</span> class </h1></div>

    <section class="desc markdown">
      <p>An entry in a doubly linked list. It contains a pointer to the next
entry, the previous entry, and the boxed element.</p>
    </section>
    

    <section class="summary offset-anchor" id="constructors">
      <h2>Constructors</h2>

      <dl class="constructor-summary-list">
        <dt id="DoubleLinkedQueueEntry" class="callable">
          <span class="name"><a href="dart-collection/DoubleLinkedQueueEntry/DoubleLinkedQueueEntry.html">DoubleLinkedQueueEntry</a></span><span class="signature">(<span class="parameter" id="-param-_element"><span class="type-annotation">E</span> <span class="parameter-name">_element</span></span>)</span>
        </dt>
        <dd>
          
        </dd>
      </dl>
    </section>

    <section class="summary offset-anchor" id="instance-properties">
      <h2>Properties</h2>

      <dl class="properties">
        <dt id="element" class="property">
          <span class="name"><a href="dart-collection/DoubleLinkedQueueEntry/element.html">element</a></span>
          <span class="signature">&#8596; E</span>         
        </dt>
        <dd>
          The element in the queue.
                  <div class="features">read / write</div>
</dd>
        <dt id="hashCode" class="property inherited">
          <span class="name"><a href="dart-core/Object/hashCode.html">hashCode</a></span>
          <span class="signature">&#8594; <a href="dart-core/int-class.html">int</a></span>         
        </dt>
        <dd class="inherited">
          The hash code for this object. <a href="dart-core/Object/hashCode.html">[...]</a>
                  <div class="features">read-only, inherited</div>
</dd>
        <dt id="runtimeType" class="property inherited">
          <span class="name"><a href="dart-core/Object/runtimeType.html">runtimeType</a></span>
          <span class="signature">&#8594; <a href="dart-core/Type-class.html">Type</a></span>         
        </dt>
        <dd class="inherited">
          A representation of the runtime type of the object.
                  <div class="features">read-only, inherited</div>
</dd>
      </dl>
    </section>

    <section class="summary offset-anchor" id="instance-methods">
      <h2>Methods</h2>
      <dl class="callables">
        <dt id="append" class="callable">
          <span class="name"><a href="dart-collection/DoubleLinkedQueueEntry/append.html">append</a></span><span class="signature">(<wbr><span class="parameter" id="append-param-e"><span class="type-annotation">E</span> <span class="parameter-name">e</span></span>)
            <span class="returntype parameter">&#8594; void</span>
          </span>
                  </dt>
        <dd>
          Appends the given <code>e</code> as entry just after this entry.
                  
</dd>
        <dt id="nextEntry" class="callable">
          <span class="name"><a href="dart-collection/DoubleLinkedQueueEntry/nextEntry.html">nextEntry</a></span><span class="signature">(<wbr>)
            <span class="returntype parameter">&#8594; <a href="dart-collection/DoubleLinkedQueueEntry-class.html">DoubleLinkedQueueEntry</a><span class="signature">&lt;<wbr><span class="type-parameter">E</span>&gt;</span></span>
          </span>
                  </dt>
        <dd>
          Returns the next entry or <code>null</code> if there is none.
                  
</dd>
        <dt id="prepend" class="callable">
          <span class="name"><a href="dart-collection/DoubleLinkedQueueEntry/prepend.html">prepend</a></span><span class="signature">(<wbr><span class="parameter" id="prepend-param-e"><span class="type-annotation">E</span> <span class="parameter-name">e</span></span>)
            <span class="returntype parameter">&#8594; void</span>
          </span>
                  </dt>
        <dd>
          Prepends the given <code>e</code> as entry just before this entry.
                  
</dd>
        <dt id="previousEntry" class="callable">
          <span class="name"><a href="dart-collection/DoubleLinkedQueueEntry/previousEntry.html">previousEntry</a></span><span class="signature">(<wbr>)
            <span class="returntype parameter">&#8594; <a href="dart-collection/DoubleLinkedQueueEntry-class.html">DoubleLinkedQueueEntry</a><span class="signature">&lt;<wbr><span class="type-parameter">E</span>&gt;</span></span>
          </span>
                  </dt>
        <dd>
          Returns the previous entry or <code>null</code> if there is none.
                  
</dd>
        <dt id="remove" class="callable">
          <span class="name"><a href="dart-collection/DoubleLinkedQueueEntry/remove.html">remove</a></span><span class="signature">(<wbr>)
            <span class="returntype parameter">&#8594; E</span>
          </span>
                  </dt>
        <dd>
          
                  
</dd>
        <dt id="noSuchMethod" class="callable inherited">
          <span class="name"><a href="dart-core/Object/noSuchMethod.html">noSuchMethod</a></span><span class="signature">(<wbr><span class="parameter" id="noSuchMethod-param-invocation"><span class="type-annotation"><a href="dart-core/Invocation-class.html">Invocation</a></span> <span class="parameter-name">invocation</span></span>)
            <span class="returntype parameter">&#8594; dynamic</span>
          </span>
                  </dt>
        <dd class="inherited">
          Invoked when a non-existent method or property is accessed. <a href="dart-core/Object/noSuchMethod.html">[...]</a>
                  <div class="features">inherited</div>
</dd>
        <dt id="toString" class="callable inherited">
          <span class="name"><a href="dart-core/Object/toString.html">toString</a></span><span class="signature">(<wbr>)
            <span class="returntype parameter">&#8594; <a href="dart-core/String-class.html">String</a></span>
          </span>
                  </dt>
        <dd class="inherited">
          A string representation of this object. <a href="dart-core/Object/toString.html">[...]</a>
                  <div class="features">inherited</div>
</dd>
      </dl>
    </section>

    <section class="summary offset-anchor inherited" id="operators">
      <h2>Operators</h2>
      <dl class="callables">
        <dt id="operator ==" class="callable inherited">
          <span class="name"><a href="dart-core/Object/operator_equals.html">operator ==</a></span><span class="signature">(<wbr><span class="parameter" id="==-param-other"><span class="type-annotation"><a href="dart-core/Object-class.html">Object</a></span> <span class="parameter-name">other</span></span>)
            <span class="returntype parameter">&#8594; <a href="dart-core/bool-class.html">bool</a></span>
          </span>
                  </dt>
        <dd class="inherited">
          The equality operator. <a href="dart-core/Object/operator_equals.html">[...]</a>
                  <div class="features">inherited</div>
</dd>
      </dl>
    </section>




  </div> <!-- /.main-content -->

  <div id="dartdoc-sidebar-right" class="col-xs-6 col-sm-6 col-md-2 sidebar sidebar-offcanvas-right">
    <ol>
    
      <li class="section-title"><a href="dart-collection/DoubleLinkedQueueEntry-class.html#constructors">Constructors</a></li>
      <li><a href="dart-collection/DoubleLinkedQueueEntry/DoubleLinkedQueueEntry.html">DoubleLinkedQueueEntry</a></li>
    
      <li class="section-title">
        <a href="dart-collection/DoubleLinkedQueueEntry-class.html#instance-properties">Properties</a>
      </li>
      <li><a href="dart-collection/DoubleLinkedQueueEntry/element.html">element</a></li>
      <li class="inherited"><a href="dart-core/Object/hashCode.html">hashCode</a></li>
      <li class="inherited"><a href="dart-core/Object/runtimeType.html">runtimeType</a></li>
    
      <li class="section-title"><a href="dart-collection/DoubleLinkedQueueEntry-class.html#instance-methods">Methods</a></li>
      <li><a href="dart-collection/DoubleLinkedQueueEntry/append.html">append</a></li>
      <li><a href="dart-collection/DoubleLinkedQueueEntry/nextEntry.html">nextEntry</a></li>
      <li><a href="dart-collection/DoubleLinkedQueueEntry/prepend.html">prepend</a></li>
      <li><a href="dart-collection/DoubleLinkedQueueEntry/previousEntry.html">previousEntry</a></li>
      <li><a href="dart-collection/DoubleLinkedQueueEntry/remove.html">remove</a></li>
      <li class="inherited"><a href="dart-core/Object/noSuchMethod.html">noSuchMethod</a></li>
      <li class="inherited"><a href="dart-core/Object/toString.html">toString</a></li>
    
      <li class="section-title inherited"><a href="dart-collection/DoubleLinkedQueueEntry-class.html#operators">Operators</a></li>
      <li class="inherited"><a href="dart-core/Object/operator_equals.html">operator ==</a></li>
    
    
    
    
    </ol>
  </div><!--/.sidebar-offcanvas-->

</main>

<footer>
  <span class="no-break">
    gms_check
      1.0.0
  </span>

  <!-- footer-text placeholder -->
</footer>

<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.2.1/jquery.min.js"></script>
<script src="static-assets/typeahead.bundle.min.js"></script>
<script src="static-assets/highlight.pack.js"></script>
<script src="static-assets/URI.js"></script>
<script src="static-assets/script.js"></script>
<!-- footer placeholder -->

</body>

</html>
