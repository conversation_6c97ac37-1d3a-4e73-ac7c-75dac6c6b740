<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, height=device-height, initial-scale=1, user-scalable=no">
  <meta name="description" content="API docs for the unicodeBomCharacterRune constant from the dart:convert library, for the Dart programming language.">
  <title>unicodeBomCharacterRune constant - dart:convert library - Dart API</title>
  <!-- required because all the links are pseudo-absolute -->
  <base href="..">

  <link href="https://fonts.googleapis.com/css?family=Source+Code+Pro:500,400i,400,300|Source+Sans+Pro:400,300,700" rel="stylesheet">
  <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
  <link rel="stylesheet" href="static-assets/github.css">
  <link rel="stylesheet" href="static-assets/styles.css">
  <link rel="icon" href="static-assets/favicon.png">
  <!-- header placeholder -->
</head>

<body>

<div id="overlay-under-drawer"></div>

<header id="title">
  <button id="sidenav-left-toggle" type="button">&nbsp;</button>
  <ol class="breadcrumbs gt-separated dark hidden-xs">
    <li><a href="index.html">gms_check</a></li>
    <li><a href="dart-convert/dart-convert-library.html">dart:convert</a></li>
    <li class="self-crumb">unicodeBomCharacterRune constant</li>
  </ol>
  <div class="self-name">unicodeBomCharacterRune</div>
  <form class="search navbar-right" role="search">
    <input type="text" id="search-box" autocomplete="off" disabled class="form-control typeahead" placeholder="Loading search...">
  </form>
</header>

<main>

  <div id="dartdoc-sidebar-left" class="col-xs-6 col-sm-3 col-md-2 sidebar sidebar-offcanvas-left">
    <header id="header-search-sidebar" class="hidden-l">
      <form class="search-sidebar" role="search">
        <input type="text" id="search-sidebar" autocomplete="off" disabled class="form-control typeahead" placeholder="Loading search...">
      </form>
    </header>
    
    <ol class="breadcrumbs gt-separated dark hidden-l" id="sidebar-nav">
      <li><a href="index.html">gms_check</a></li>
      <li><a href="dart-convert/dart-convert-library.html">dart:convert</a></li>
      <li class="self-crumb">unicodeBomCharacterRune constant</li>
    </ol>
    
    <h5>dart:convert library</h5>
    <ol>
      <li class="section-title"><a href="dart-convert/dart-convert-library.html#classes">Classes</a></li>
      <li><a href="dart-convert/AsciiCodec-class.html">AsciiCodec</a></li>
      <li><a href="dart-convert/AsciiDecoder-class.html">AsciiDecoder</a></li>
      <li><a href="dart-convert/AsciiEncoder-class.html">AsciiEncoder</a></li>
      <li><a href="dart-convert/Base64Codec-class.html">Base64Codec</a></li>
      <li><a href="dart-convert/Base64Decoder-class.html">Base64Decoder</a></li>
      <li><a href="dart-convert/Base64Encoder-class.html">Base64Encoder</a></li>
      <li><a href="dart-convert/ByteConversionSink-class.html">ByteConversionSink</a></li>
      <li><a href="dart-convert/ByteConversionSinkBase-class.html">ByteConversionSinkBase</a></li>
      <li><a href="dart-convert/ChunkedConversionSink-class.html">ChunkedConversionSink</a></li>
      <li><a href="dart-convert/ClosableStringSink-class.html">ClosableStringSink</a></li>
      <li><a href="dart-convert/Codec-class.html">Codec</a></li>
      <li><a href="dart-convert/Converter-class.html">Converter</a></li>
      <li><a href="dart-convert/Encoding-class.html">Encoding</a></li>
      <li><a href="dart-convert/HtmlEscape-class.html">HtmlEscape</a></li>
      <li><a href="dart-convert/HtmlEscapeMode-class.html">HtmlEscapeMode</a></li>
      <li><a href="dart-convert/JsonCodec-class.html">JsonCodec</a></li>
      <li><a href="dart-convert/JsonDecoder-class.html">JsonDecoder</a></li>
      <li><a href="dart-convert/JsonEncoder-class.html">JsonEncoder</a></li>
      <li><a href="dart-convert/JsonUtf8Encoder-class.html">JsonUtf8Encoder</a></li>
      <li><a href="dart-convert/Latin1Codec-class.html">Latin1Codec</a></li>
      <li><a href="dart-convert/Latin1Decoder-class.html">Latin1Decoder</a></li>
      <li><a href="dart-convert/Latin1Encoder-class.html">Latin1Encoder</a></li>
      <li><a href="dart-convert/LineSplitter-class.html">LineSplitter</a></li>
      <li><a href="dart-convert/StringConversionSink-class.html">StringConversionSink</a></li>
      <li><a href="dart-convert/StringConversionSinkBase-class.html">StringConversionSinkBase</a></li>
      <li><a href="dart-convert/StringConversionSinkMixin-class.html">StringConversionSinkMixin</a></li>
      <li><a href="dart-convert/Utf8Codec-class.html">Utf8Codec</a></li>
      <li><a href="dart-convert/Utf8Decoder-class.html">Utf8Decoder</a></li>
      <li><a href="dart-convert/Utf8Encoder-class.html">Utf8Encoder</a></li>
    
    
    
      <li class="section-title"><a href="dart-convert/dart-convert-library.html#constants">Constants</a></li>
      <li><a href="dart-convert/ascii-constant.html">ascii</a></li>
      <li><a href="dart-convert/base64-constant.html">base64</a></li>
      <li><a href="dart-convert/base64Url-constant.html">base64Url</a></li>
      <li><a href="dart-convert/htmlEscape-constant.html">htmlEscape</a></li>
      <li><a href="dart-convert/json-constant.html">json</a></li>
      <li><a href="dart-convert/latin1-constant.html">latin1</a></li>
      <li><a href="dart-convert/unicodeBomCharacterRune-constant.html">unicodeBomCharacterRune</a></li>
      <li><a href="dart-convert/unicodeReplacementCharacterRune-constant.html">unicodeReplacementCharacterRune</a></li>
      <li><a href="dart-convert/utf8-constant.html">utf8</a></li>
    
    
      <li class="section-title"><a href="dart-convert/dart-convert-library.html#functions">Functions</a></li>
      <li><a href="dart-convert/base64Decode.html">base64Decode</a></li>
      <li><a href="dart-convert/base64Encode.html">base64Encode</a></li>
      <li><a href="dart-convert/base64UrlEncode.html">base64UrlEncode</a></li>
      <li><a href="dart-convert/jsonDecode.html">jsonDecode</a></li>
      <li><a href="dart-convert/jsonEncode.html">jsonEncode</a></li>
    
    
    
      <li class="section-title"><a href="dart-convert/dart-convert-library.html#exceptions">Exceptions</a></li>
      <li><a href="dart-convert/JsonCyclicError-class.html">JsonCyclicError</a></li>
      <li><a href="dart-convert/JsonUnsupportedObjectError-class.html">JsonUnsupportedObjectError</a></li>
    </ol>
  </div><!--/.sidebar-offcanvas-left-->

  <div id="dartdoc-main-content" class="col-xs-12 col-sm-9 col-md-8 main-content">
      <div><h1><span class="kind-top-level-constant">unicodeBomCharacterRune</span> top-level constant </h1></div>

      <section class="multi-line-signature">
        const <span class="name ">unicodeBomCharacterRune</span>
        =
        <span class="constant-value">0xFEFF</span>
        
      </section>
      <section class="desc markdown">
        <p>The Unicode Byte Order Marker (BOM) character <code>U+FEFF</code>.</p>
      </section>
            <section class="summary source-code" id="source">
        <h2><span>Implementation</span></h2>
        <pre class="language-dart"><code class="language-dart">const int unicodeBomCharacterRune = 0xFEFF</code></pre>
      </section>

  </div> <!-- /.main-content -->

  <div id="dartdoc-sidebar-right" class="col-xs-6 col-sm-6 col-md-2 sidebar sidebar-offcanvas-right">
  </div><!--/.sidebar-offcanvas-->

</main>

<footer>
  <span class="no-break">
    gms_check
      1.0.0
  </span>

  <!-- footer-text placeholder -->
</footer>

<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.2.1/jquery.min.js"></script>
<script src="static-assets/typeahead.bundle.min.js"></script>
<script src="static-assets/highlight.pack.js"></script>
<script src="static-assets/URI.js"></script>
<script src="static-assets/script.js"></script>
<!-- footer placeholder -->

</body>

</html>
