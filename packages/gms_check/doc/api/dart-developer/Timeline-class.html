<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, height=device-height, initial-scale=1, user-scalable=no">
  <meta name="description" content="API docs for the Timeline class from the dart:developer library, for the Dart programming language.">
  <title>Timeline class - dart:developer library - Dart API</title>
  <!-- required because all the links are pseudo-absolute -->
  <base href="..">

  <link href="https://fonts.googleapis.com/css?family=Source+Code+Pro:500,400i,400,300|Source+Sans+Pro:400,300,700" rel="stylesheet">
  <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
  <link rel="stylesheet" href="static-assets/github.css">
  <link rel="stylesheet" href="static-assets/styles.css">
  <link rel="icon" href="static-assets/favicon.png">
  <!-- header placeholder -->
</head>

<body>

<div id="overlay-under-drawer"></div>

<header id="title">
  <button id="sidenav-left-toggle" type="button">&nbsp;</button>
  <ol class="breadcrumbs gt-separated dark hidden-xs">
    <li><a href="index.html">gms_check</a></li>
    <li><a href="dart-developer/dart-developer-library.html">dart:developer</a></li>
    <li class="self-crumb">Timeline class</li>
  </ol>
  <div class="self-name">Timeline</div>
  <form class="search navbar-right" role="search">
    <input type="text" id="search-box" autocomplete="off" disabled class="form-control typeahead" placeholder="Loading search...">
  </form>
</header>

<main>

  <div id="dartdoc-sidebar-left" class="col-xs-6 col-sm-3 col-md-2 sidebar sidebar-offcanvas-left">
    <header id="header-search-sidebar" class="hidden-l">
      <form class="search-sidebar" role="search">
        <input type="text" id="search-sidebar" autocomplete="off" disabled class="form-control typeahead" placeholder="Loading search...">
      </form>
    </header>
    
    <ol class="breadcrumbs gt-separated dark hidden-l" id="sidebar-nav">
      <li><a href="index.html">gms_check</a></li>
      <li><a href="dart-developer/dart-developer-library.html">dart:developer</a></li>
      <li class="self-crumb">Timeline class</li>
    </ol>
    
    <h5>dart:developer library</h5>
    <ol>
      <li class="section-title"><a href="dart-developer/dart-developer-library.html#classes">Classes</a></li>
      <li><a href="dart-developer/Counter-class.html">Counter</a></li>
      <li><a href="dart-developer/Flow-class.html">Flow</a></li>
      <li><a href="dart-developer/Gauge-class.html">Gauge</a></li>
      <li><a href="dart-developer/Metric-class.html">Metric</a></li>
      <li><a href="dart-developer/Metrics-class.html">Metrics</a></li>
      <li><a href="dart-developer/Service-class.html">Service</a></li>
      <li><a href="dart-developer/ServiceExtensionResponse-class.html">ServiceExtensionResponse</a></li>
      <li><a href="dart-developer/ServiceProtocolInfo-class.html">ServiceProtocolInfo</a></li>
      <li><a href="dart-developer/Timeline-class.html">Timeline</a></li>
      <li><a href="dart-developer/TimelineTask-class.html">TimelineTask</a></li>
      <li><a href="dart-developer/UserTag-class.html">UserTag</a></li>
    
    
    
    
    
      <li class="section-title"><a href="dart-developer/dart-developer-library.html#functions">Functions</a></li>
      <li><a href="dart-developer/debugger.html">debugger</a></li>
      <li><a href="dart-developer/getCurrentTag.html">getCurrentTag</a></li>
      <li><a href="dart-developer/inspect.html">inspect</a></li>
      <li><a href="dart-developer/log.html">log</a></li>
      <li><a href="dart-developer/postEvent.html">postEvent</a></li>
      <li><a href="dart-developer/registerExtension.html">registerExtension</a></li>
    
    
      <li class="section-title"><a href="dart-developer/dart-developer-library.html#typedefs">Typedefs</a></li>
      <li><a href="dart-developer/ServiceExtensionHandler.html">ServiceExtensionHandler</a></li>
      <li><a href="dart-developer/TimelineAsyncFunction.html">TimelineAsyncFunction</a></li>
      <li><a href="dart-developer/TimelineSyncFunction.html">TimelineSyncFunction</a></li>
    
    </ol>
  </div>

  <div id="dartdoc-main-content" class="col-xs-12 col-sm-9 col-md-8 main-content">
      <div><h1><span class="kind-class">Timeline</span> class </h1></div>

    <section class="desc markdown">
      <p>Add to the timeline.</p>
<p><a href="dart-developer/Timeline-class.html">Timeline</a>'s methods add synchronous events to the timeline. When
generating a timeline in Chrome's tracing format, using <a href="dart-developer/Timeline-class.html">Timeline</a> generates
"Complete" events. <a href="dart-developer/Timeline-class.html">Timeline</a>'s <a href="dart-developer/Timeline/startSync.html">startSync</a> and <a href="dart-developer/Timeline/finishSync.html">finishSync</a> can be used
explicitly, or implicitly by wrapping a closure in <a href="dart-developer/Timeline/timeSync.html">timeSync</a>. For example:</p>
<pre class="language-dart"><code class="language-dart">Timeline.startSync("Doing Something");
doSomething();
Timeline.finishSync();
</code></pre>
<p>Or:</p>
<pre class="language-dart"><code class="language-dart">Timeline.timeSync("Doing Something", () {
  doSomething();
});
</code></pre>
    </section>
    

    <section class="summary offset-anchor" id="constructors">
      <h2>Constructors</h2>

      <dl class="constructor-summary-list">
        <dt id="Timeline" class="callable">
          <span class="name"><a href="dart-developer/Timeline/Timeline.html">Timeline</a></span><span class="signature">()</span>
        </dt>
        <dd>
          
        </dd>
      </dl>
    </section>

    <section class="summary offset-anchor inherited" id="instance-properties">
      <h2>Properties</h2>

      <dl class="properties">
        <dt id="hashCode" class="property inherited">
          <span class="name"><a href="dart-core/Object/hashCode.html">hashCode</a></span>
          <span class="signature">&#8594; <a href="dart-core/int-class.html">int</a></span>         
        </dt>
        <dd class="inherited">
          The hash code for this object. <a href="dart-core/Object/hashCode.html">[...]</a>
                  <div class="features">read-only, inherited</div>
</dd>
        <dt id="runtimeType" class="property inherited">
          <span class="name"><a href="dart-core/Object/runtimeType.html">runtimeType</a></span>
          <span class="signature">&#8594; <a href="dart-core/Type-class.html">Type</a></span>         
        </dt>
        <dd class="inherited">
          A representation of the runtime type of the object.
                  <div class="features">read-only, inherited</div>
</dd>
      </dl>
    </section>

    <section class="summary offset-anchor inherited" id="instance-methods">
      <h2>Methods</h2>
      <dl class="callables">
        <dt id="noSuchMethod" class="callable inherited">
          <span class="name"><a href="dart-core/Object/noSuchMethod.html">noSuchMethod</a></span><span class="signature">(<wbr><span class="parameter" id="noSuchMethod-param-invocation"><span class="type-annotation"><a href="dart-core/Invocation-class.html">Invocation</a></span> <span class="parameter-name">invocation</span></span>)
            <span class="returntype parameter">&#8594; dynamic</span>
          </span>
                  </dt>
        <dd class="inherited">
          Invoked when a non-existent method or property is accessed. <a href="dart-core/Object/noSuchMethod.html">[...]</a>
                  <div class="features">inherited</div>
</dd>
        <dt id="toString" class="callable inherited">
          <span class="name"><a href="dart-core/Object/toString.html">toString</a></span><span class="signature">(<wbr>)
            <span class="returntype parameter">&#8594; <a href="dart-core/String-class.html">String</a></span>
          </span>
                  </dt>
        <dd class="inherited">
          A string representation of this object. <a href="dart-core/Object/toString.html">[...]</a>
                  <div class="features">inherited</div>
</dd>
      </dl>
    </section>

    <section class="summary offset-anchor inherited" id="operators">
      <h2>Operators</h2>
      <dl class="callables">
        <dt id="operator ==" class="callable inherited">
          <span class="name"><a href="dart-core/Object/operator_equals.html">operator ==</a></span><span class="signature">(<wbr><span class="parameter" id="==-param-other"><span class="type-annotation"><a href="dart-core/Object-class.html">Object</a></span> <span class="parameter-name">other</span></span>)
            <span class="returntype parameter">&#8594; <a href="dart-core/bool-class.html">bool</a></span>
          </span>
                  </dt>
        <dd class="inherited">
          The equality operator. <a href="dart-core/Object/operator_equals.html">[...]</a>
                  <div class="features">inherited</div>
</dd>
      </dl>
    </section>

    <section class="summary offset-anchor" id="static-properties">
      <h2>Static Properties</h2>

      <dl class="properties">
        <dt id="now" class="property">
          <span class="name"><a href="dart-developer/Timeline/now.html">now</a></span>
          <span class="signature">&#8594; <a href="dart-core/int-class.html">int</a></span>         
        </dt>
        <dd>
          The current time stamp from the clock used by the timeline. Units are
microseconds. <a href="dart-developer/Timeline/now.html">[...]</a>
                  <div class="features">read-only</div>
</dd>
      </dl>
    </section>

    <section class="summary offset-anchor" id="static-methods">
      <h2>Static Methods</h2>
      <dl class="callables">
        <dt id="finishSync" class="callable">
          <span class="name"><a href="dart-developer/Timeline/finishSync.html">finishSync</a></span><span class="signature">(<wbr>)
            <span class="returntype parameter">&#8594; void</span>
          </span>
                  </dt>
        <dd>
          Finish the last synchronous operation that was started.
                  
</dd>
        <dt id="instantSync" class="callable">
          <span class="name"><a href="dart-developer/Timeline/instantSync.html">instantSync</a></span><span class="signature">(<wbr><span class="parameter" id="instantSync-param-name"><span class="type-annotation"><a href="dart-core/String-class.html">String</a></span> <span class="parameter-name">name</span>, {</span> <span class="parameter" id="instantSync-param-arguments"><span class="type-annotation"><a href="dart-core/Map-class.html">Map</a></span> <span class="parameter-name">arguments</span></span> })
            <span class="returntype parameter">&#8594; void</span>
          </span>
                  </dt>
        <dd>
          Emit an instant event.
                  
</dd>
        <dt id="startSync" class="callable">
          <span class="name"><a href="dart-developer/Timeline/startSync.html">startSync</a></span><span class="signature">(<wbr><span class="parameter" id="startSync-param-name"><span class="type-annotation"><a href="dart-core/String-class.html">String</a></span> <span class="parameter-name">name</span>, {</span> <span class="parameter" id="startSync-param-arguments"><span class="type-annotation"><a href="dart-core/Map-class.html">Map</a></span> <span class="parameter-name">arguments</span>, </span> <span class="parameter" id="startSync-param-flow"><span class="type-annotation"><a href="dart-developer/Flow-class.html">Flow</a></span> <span class="parameter-name">flow</span></span> })
            <span class="returntype parameter">&#8594; void</span>
          </span>
                  </dt>
        <dd>
          Start a synchronous operation labeled <code>name</code>. Optionally takes
a <a href="dart-core/Map-class.html">Map</a> of <code>arguments</code>. This slice may also optionally be associated with
a <a href="dart-developer/Flow-class.html">Flow</a> event. This operation must be finished before
returning to the event queue.
                  
</dd>
        <dt id="timeSync" class="callable">
          <span class="name"><a href="dart-developer/Timeline/timeSync.html">timeSync</a></span><span class="signature">&lt;<wbr><span class="type-parameter">T</span>&gt;</span><span class="signature">(<wbr><span class="parameter" id="timeSync-param-name"><span class="type-annotation"><a href="dart-core/String-class.html">String</a></span> <span class="parameter-name">name</span>, </span> <span class="parameter" id="timeSync-param-function"><span class="type-annotation"><a href="dart-developer/TimelineSyncFunction.html">TimelineSyncFunction</a><span class="signature">&lt;<wbr><span class="type-parameter">T</span>&gt;</span></span> <span class="parameter-name">function</span>, {</span> <span class="parameter" id="timeSync-param-arguments"><span class="type-annotation"><a href="dart-core/Map-class.html">Map</a></span> <span class="parameter-name">arguments</span>, </span> <span class="parameter" id="timeSync-param-flow"><span class="type-annotation"><a href="dart-developer/Flow-class.html">Flow</a></span> <span class="parameter-name">flow</span></span> })
            <span class="returntype parameter">&#8594; T</span>
          </span>
                  </dt>
        <dd>
          A utility method to time a synchronous <code>function</code>. Internally calls
<code>function</code> bracketed by calls to <a href="dart-developer/Timeline/startSync.html">startSync</a> and <a href="dart-developer/Timeline/finishSync.html">finishSync</a>.
                  
</dd>
      </dl>
    </section>


  </div> <!-- /.main-content -->

  <div id="dartdoc-sidebar-right" class="col-xs-6 col-sm-6 col-md-2 sidebar sidebar-offcanvas-right">
    <ol>
    
      <li class="section-title"><a href="dart-developer/Timeline-class.html#constructors">Constructors</a></li>
      <li><a href="dart-developer/Timeline/Timeline.html">Timeline</a></li>
    
      <li class="section-title inherited">
        <a href="dart-developer/Timeline-class.html#instance-properties">Properties</a>
      </li>
      <li class="inherited"><a href="dart-core/Object/hashCode.html">hashCode</a></li>
      <li class="inherited"><a href="dart-core/Object/runtimeType.html">runtimeType</a></li>
    
      <li class="section-title inherited"><a href="dart-developer/Timeline-class.html#instance-methods">Methods</a></li>
      <li class="inherited"><a href="dart-core/Object/noSuchMethod.html">noSuchMethod</a></li>
      <li class="inherited"><a href="dart-core/Object/toString.html">toString</a></li>
    
      <li class="section-title inherited"><a href="dart-developer/Timeline-class.html#operators">Operators</a></li>
      <li class="inherited"><a href="dart-core/Object/operator_equals.html">operator ==</a></li>
    
      <li class="section-title"><a href="dart-developer/Timeline-class.html#static-properties">Static properties</a></li>
      <li><a href="dart-developer/Timeline/now.html">now</a></li>
    
      <li class="section-title"><a href="dart-developer/Timeline-class.html#static-methods">Static methods</a></li>
      <li><a href="dart-developer/Timeline/finishSync.html">finishSync</a></li>
      <li><a href="dart-developer/Timeline/instantSync.html">instantSync</a></li>
      <li><a href="dart-developer/Timeline/startSync.html">startSync</a></li>
      <li><a href="dart-developer/Timeline/timeSync.html">timeSync</a></li>
    
    
    </ol>
  </div><!--/.sidebar-offcanvas-->

</main>

<footer>
  <span class="no-break">
    gms_check
      1.0.0
  </span>

  <!-- footer-text placeholder -->
</footer>

<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.2.1/jquery.min.js"></script>
<script src="static-assets/typeahead.bundle.min.js"></script>
<script src="static-assets/highlight.pack.js"></script>
<script src="static-assets/URI.js"></script>
<script src="static-assets/script.js"></script>
<!-- footer placeholder -->

</body>

</html>
