<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, height=device-height, initial-scale=1, user-scalable=no">
  <meta name="description" content="API docs for the timeSync method from the Timeline class, for the Dart programming language.">
  <title>timeSync method - Timeline class - dart:developer library - Dart API</title>
  <!-- required because all the links are pseudo-absolute -->
  <base href="../..">

  <link href="https://fonts.googleapis.com/css?family=Source+Code+Pro:500,400i,400,300|Source+Sans+Pro:400,300,700" rel="stylesheet">
  <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
  <link rel="stylesheet" href="static-assets/github.css">
  <link rel="stylesheet" href="static-assets/styles.css">
  <link rel="icon" href="static-assets/favicon.png">
  <!-- header placeholder -->
</head>

<body>

<div id="overlay-under-drawer"></div>

<header id="title">
  <button id="sidenav-left-toggle" type="button">&nbsp;</button>
  <ol class="breadcrumbs gt-separated dark hidden-xs">
    <li><a href="index.html">gms_check</a></li>
    <li><a href="dart-developer/dart-developer-library.html">dart:developer</a></li>
    <li><a href="dart-developer/Timeline-class.html">Timeline</a></li>
    <li class="self-crumb">timeSync&lt;<wbr><span class="type-parameter">T</span>&gt; method</li>
  </ol>
  <div class="self-name">timeSync</div>
  <form class="search navbar-right" role="search">
    <input type="text" id="search-box" autocomplete="off" disabled class="form-control typeahead" placeholder="Loading search...">
  </form>
</header>

<main>

  <div id="dartdoc-sidebar-left" class="col-xs-6 col-sm-3 col-md-2 sidebar sidebar-offcanvas-left">
    <header id="header-search-sidebar" class="hidden-l">
      <form class="search-sidebar" role="search">
        <input type="text" id="search-sidebar" autocomplete="off" disabled class="form-control typeahead" placeholder="Loading search...">
      </form>
    </header>
    
    <ol class="breadcrumbs gt-separated dark hidden-l" id="sidebar-nav">
      <li><a href="index.html">gms_check</a></li>
      <li><a href="dart-developer/dart-developer-library.html">dart:developer</a></li>
      <li><a href="dart-developer/Timeline-class.html">Timeline</a></li>
      <li class="self-crumb">timeSync&lt;<wbr><span class="type-parameter">T</span>&gt; method</li>
    </ol>
    
    <h5>Timeline class</h5>
    <ol>
    
        <li class="section-title"><a href="dart-developer/Timeline-class.html#constructors">Constructors</a></li>
        <li><a href="dart-developer/Timeline/Timeline.html">Timeline</a></li>
    
        <li class="section-title inherited">
            <a href="dart-developer/Timeline-class.html#instance-properties">Properties</a>
        </li>
        <li class="inherited"><a href="dart-core/Object/hashCode.html">hashCode</a></li>
        <li class="inherited"><a href="dart-core/Object/runtimeType.html">runtimeType</a></li>
    
        <li class="section-title inherited"><a href="dart-developer/Timeline-class.html#instance-methods">Methods</a></li>
        <li class="inherited"><a href="dart-core/Object/noSuchMethod.html">noSuchMethod</a></li>
        <li class="inherited"><a href="dart-core/Object/toString.html">toString</a></li>
    
        <li class="section-title inherited"><a href="dart-developer/Timeline-class.html#operators">Operators</a></li>
        <li class="inherited"><a href="dart-core/Object/operator_equals.html">operator ==</a></li>
    
        <li class="section-title"><a href="dart-developer/Timeline-class.html#static-properties">Static properties</a></li>
        <li><a href="dart-developer/Timeline/now.html">now</a></li>
    
        <li class="section-title"><a href="dart-developer/Timeline-class.html#static-methods">Static methods</a></li>
        <li><a href="dart-developer/Timeline/finishSync.html">finishSync</a></li>
        <li><a href="dart-developer/Timeline/instantSync.html">instantSync</a></li>
        <li><a href="dart-developer/Timeline/startSync.html">startSync</a></li>
        <li><a href="dart-developer/Timeline/timeSync.html">timeSync</a></li>
    
    
    
    </ol>
  </div><!--/.sidebar-offcanvas-->

  <div id="dartdoc-main-content" class="col-xs-12 col-sm-9 col-md-8 main-content">
      <div><h1><span class="kind-method">timeSync&lt;<wbr><span class="type-parameter">T</span>&gt;</span> method</h1></div>

    <section class="multi-line-signature">
      <span class="returntype">T</span>
            <span class="name ">timeSync</span>
&lt;<wbr><span class="type-parameter">T</span>&gt;(<wbr><span class="parameter" id="timeSync-param-name"><span class="type-annotation"><a href="dart-core/String-class.html">String</a></span> <span class="parameter-name">name</span>, </span> <span class="parameter" id="timeSync-param-function"><span class="type-annotation"><a href="dart-developer/TimelineSyncFunction.html">TimelineSyncFunction</a><span class="signature">&lt;<wbr><span class="type-parameter">T</span>&gt;</span></span> <span class="parameter-name">function</span>, {</span> <span class="parameter" id="timeSync-param-arguments"><span class="type-annotation"><a href="dart-core/Map-class.html">Map</a></span> <span class="parameter-name">arguments</span>, </span> <span class="parameter" id="timeSync-param-flow"><span class="type-annotation"><a href="dart-developer/Flow-class.html">Flow</a></span> <span class="parameter-name">flow</span></span> })
      
    </section>
    <section class="desc markdown">
      <p>A utility method to time a synchronous <code>function</code>. Internally calls
<code>function</code> bracketed by calls to <a href="dart-developer/Timeline/startSync.html">startSync</a> and <a href="dart-developer/Timeline/finishSync.html">finishSync</a>.</p>
    </section>
    
    <section class="summary source-code" id="source">
      <h2><span>Implementation</span></h2>
      <pre class="language-dart"><code class="language-dart">static T timeSync&lt;T&gt;(String name, TimelineSyncFunction&lt;T&gt; function,
    {Map? arguments, Flow? flow}) {
  startSync(name, arguments: arguments, flow: flow);
  try {
    return function();
  } finally {
    finishSync();
  }
}</code></pre>
    </section>

  </div> <!-- /.main-content -->

  <div id="dartdoc-sidebar-right" class="col-xs-6 col-sm-6 col-md-2 sidebar sidebar-offcanvas-right">
  </div><!--/.sidebar-offcanvas-->

</main>

<footer>
  <span class="no-break">
    gms_check
      1.0.0
  </span>

  <!-- footer-text placeholder -->
</footer>

<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.2.1/jquery.min.js"></script>
<script src="static-assets/typeahead.bundle.min.js"></script>
<script src="static-assets/highlight.pack.js"></script>
<script src="static-assets/URI.js"></script>
<script src="static-assets/script.js"></script>
<!-- footer placeholder -->

</body>

</html>
