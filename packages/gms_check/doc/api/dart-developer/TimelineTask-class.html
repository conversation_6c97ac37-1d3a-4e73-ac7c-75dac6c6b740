<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, height=device-height, initial-scale=1, user-scalable=no">
  <meta name="description" content="API docs for the TimelineTask class from the dart:developer library, for the Dart programming language.">
  <title>TimelineTask class - dart:developer library - Dart API</title>
  <!-- required because all the links are pseudo-absolute -->
  <base href="..">

  <link href="https://fonts.googleapis.com/css?family=Source+Code+Pro:500,400i,400,300|Source+Sans+Pro:400,300,700" rel="stylesheet">
  <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
  <link rel="stylesheet" href="static-assets/github.css">
  <link rel="stylesheet" href="static-assets/styles.css">
  <link rel="icon" href="static-assets/favicon.png">
  <!-- header placeholder -->
</head>

<body>

<div id="overlay-under-drawer"></div>

<header id="title">
  <button id="sidenav-left-toggle" type="button">&nbsp;</button>
  <ol class="breadcrumbs gt-separated dark hidden-xs">
    <li><a href="index.html">gms_check</a></li>
    <li><a href="dart-developer/dart-developer-library.html">dart:developer</a></li>
    <li class="self-crumb">TimelineTask class</li>
  </ol>
  <div class="self-name">TimelineTask</div>
  <form class="search navbar-right" role="search">
    <input type="text" id="search-box" autocomplete="off" disabled class="form-control typeahead" placeholder="Loading search...">
  </form>
</header>

<main>

  <div id="dartdoc-sidebar-left" class="col-xs-6 col-sm-3 col-md-2 sidebar sidebar-offcanvas-left">
    <header id="header-search-sidebar" class="hidden-l">
      <form class="search-sidebar" role="search">
        <input type="text" id="search-sidebar" autocomplete="off" disabled class="form-control typeahead" placeholder="Loading search...">
      </form>
    </header>
    
    <ol class="breadcrumbs gt-separated dark hidden-l" id="sidebar-nav">
      <li><a href="index.html">gms_check</a></li>
      <li><a href="dart-developer/dart-developer-library.html">dart:developer</a></li>
      <li class="self-crumb">TimelineTask class</li>
    </ol>
    
    <h5>dart:developer library</h5>
    <ol>
      <li class="section-title"><a href="dart-developer/dart-developer-library.html#classes">Classes</a></li>
      <li><a href="dart-developer/Counter-class.html">Counter</a></li>
      <li><a href="dart-developer/Flow-class.html">Flow</a></li>
      <li><a href="dart-developer/Gauge-class.html">Gauge</a></li>
      <li><a href="dart-developer/Metric-class.html">Metric</a></li>
      <li><a href="dart-developer/Metrics-class.html">Metrics</a></li>
      <li><a href="dart-developer/Service-class.html">Service</a></li>
      <li><a href="dart-developer/ServiceExtensionResponse-class.html">ServiceExtensionResponse</a></li>
      <li><a href="dart-developer/ServiceProtocolInfo-class.html">ServiceProtocolInfo</a></li>
      <li><a href="dart-developer/Timeline-class.html">Timeline</a></li>
      <li><a href="dart-developer/TimelineTask-class.html">TimelineTask</a></li>
      <li><a href="dart-developer/UserTag-class.html">UserTag</a></li>
    
    
    
    
    
      <li class="section-title"><a href="dart-developer/dart-developer-library.html#functions">Functions</a></li>
      <li><a href="dart-developer/debugger.html">debugger</a></li>
      <li><a href="dart-developer/getCurrentTag.html">getCurrentTag</a></li>
      <li><a href="dart-developer/inspect.html">inspect</a></li>
      <li><a href="dart-developer/log.html">log</a></li>
      <li><a href="dart-developer/postEvent.html">postEvent</a></li>
      <li><a href="dart-developer/registerExtension.html">registerExtension</a></li>
    
    
      <li class="section-title"><a href="dart-developer/dart-developer-library.html#typedefs">Typedefs</a></li>
      <li><a href="dart-developer/ServiceExtensionHandler.html">ServiceExtensionHandler</a></li>
      <li><a href="dart-developer/TimelineAsyncFunction.html">TimelineAsyncFunction</a></li>
      <li><a href="dart-developer/TimelineSyncFunction.html">TimelineSyncFunction</a></li>
    
    </ol>
  </div>

  <div id="dartdoc-main-content" class="col-xs-12 col-sm-9 col-md-8 main-content">
      <div><h1><span class="kind-class">TimelineTask</span> class </h1></div>

    <section class="desc markdown">
      <p>An asynchronous task on the timeline. An asynchronous task can have many
(nested) synchronous operations. Synchronous operations can live longer than
the current isolate event. To pass a <a href="dart-developer/TimelineTask-class.html">TimelineTask</a> to another isolate,
you must first call <a href="dart-developer/TimelineTask/pass.html">pass</a> to get the task id and then construct a new
<a href="dart-developer/TimelineTask-class.html">TimelineTask</a> in the other isolate.</p>
    </section>
    

    <section class="summary offset-anchor" id="constructors">
      <h2>Constructors</h2>

      <dl class="constructor-summary-list">
        <dt id="TimelineTask" class="callable">
          <span class="name"><a href="dart-developer/TimelineTask/TimelineTask.html">TimelineTask</a></span><span class="signature">({<span class="parameter" id="-param-parent"><span class="type-annotation"><a href="dart-developer/TimelineTask-class.html">TimelineTask</a></span> <span class="parameter-name">parent</span>, </span> <span class="parameter" id="-param-filterKey"><span class="type-annotation"><a href="dart-core/String-class.html">String</a></span> <span class="parameter-name">filterKey</span></span> })</span>
        </dt>
        <dd>
          Create a task. The task ID will be set by the system. <a href="dart-developer/TimelineTask/TimelineTask.html">[...]</a>
        </dd>
        <dt id="TimelineTask.withTaskId" class="callable">
          <span class="name"><a href="dart-developer/TimelineTask/TimelineTask.withTaskId.html">TimelineTask.withTaskId</a></span><span class="signature">(<span class="parameter" id="withTaskId-param-taskId"><span class="type-annotation"><a href="dart-core/int-class.html">int</a></span> <span class="parameter-name">taskId</span>, {</span> <span class="parameter" id="withTaskId-param-filterKey"><span class="type-annotation"><a href="dart-core/String-class.html">String</a></span> <span class="parameter-name">filterKey</span></span> })</span>
        </dt>
        <dd>
          Create a task with an explicit <code>taskId</code>. This is useful if you are
passing a task from one isolate to another. <a href="dart-developer/TimelineTask/TimelineTask.withTaskId.html">[...]</a>
        </dd>
      </dl>
    </section>

    <section class="summary offset-anchor inherited" id="instance-properties">
      <h2>Properties</h2>

      <dl class="properties">
        <dt id="hashCode" class="property inherited">
          <span class="name"><a href="dart-core/Object/hashCode.html">hashCode</a></span>
          <span class="signature">&#8594; <a href="dart-core/int-class.html">int</a></span>         
        </dt>
        <dd class="inherited">
          The hash code for this object. <a href="dart-core/Object/hashCode.html">[...]</a>
                  <div class="features">read-only, inherited</div>
</dd>
        <dt id="runtimeType" class="property inherited">
          <span class="name"><a href="dart-core/Object/runtimeType.html">runtimeType</a></span>
          <span class="signature">&#8594; <a href="dart-core/Type-class.html">Type</a></span>         
        </dt>
        <dd class="inherited">
          A representation of the runtime type of the object.
                  <div class="features">read-only, inherited</div>
</dd>
      </dl>
    </section>

    <section class="summary offset-anchor" id="instance-methods">
      <h2>Methods</h2>
      <dl class="callables">
        <dt id="finish" class="callable">
          <span class="name"><a href="dart-developer/TimelineTask/finish.html">finish</a></span><span class="signature">(<wbr>{<span class="parameter" id="finish-param-arguments"><span class="type-annotation"><a href="dart-core/Map-class.html">Map</a></span> <span class="parameter-name">arguments</span></span> })
            <span class="returntype parameter">&#8594; void</span>
          </span>
                  </dt>
        <dd>
          Finish the last synchronous operation that was started.
Optionally takes a <a href="dart-core/Map-class.html">Map</a> of <code>arguments</code>.
                  
</dd>
        <dt id="instant" class="callable">
          <span class="name"><a href="dart-developer/TimelineTask/instant.html">instant</a></span><span class="signature">(<wbr><span class="parameter" id="instant-param-name"><span class="type-annotation"><a href="dart-core/String-class.html">String</a></span> <span class="parameter-name">name</span>, {</span> <span class="parameter" id="instant-param-arguments"><span class="type-annotation"><a href="dart-core/Map-class.html">Map</a></span> <span class="parameter-name">arguments</span></span> })
            <span class="returntype parameter">&#8594; void</span>
          </span>
                  </dt>
        <dd>
          Emit an instant event for this task.
Optionally takes a <a href="dart-core/Map-class.html">Map</a> of <code>arguments</code>.
                  
</dd>
        <dt id="pass" class="callable">
          <span class="name"><a href="dart-developer/TimelineTask/pass.html">pass</a></span><span class="signature">(<wbr>)
            <span class="returntype parameter">&#8594; <a href="dart-core/int-class.html">int</a></span>
          </span>
                  </dt>
        <dd>
          Retrieve the <a href="dart-developer/TimelineTask-class.html">TimelineTask</a>'s task id. Will throw an exception if the
stack is not empty.
                  
</dd>
        <dt id="start" class="callable">
          <span class="name"><a href="dart-developer/TimelineTask/start.html">start</a></span><span class="signature">(<wbr><span class="parameter" id="start-param-name"><span class="type-annotation"><a href="dart-core/String-class.html">String</a></span> <span class="parameter-name">name</span>, {</span> <span class="parameter" id="start-param-arguments"><span class="type-annotation"><a href="dart-core/Map-class.html">Map</a></span> <span class="parameter-name">arguments</span></span> })
            <span class="returntype parameter">&#8594; void</span>
          </span>
                  </dt>
        <dd>
          Start a synchronous operation within this task named <code>name</code>.
Optionally takes a <a href="dart-core/Map-class.html">Map</a> of <code>arguments</code>.
                  
</dd>
        <dt id="noSuchMethod" class="callable inherited">
          <span class="name"><a href="dart-core/Object/noSuchMethod.html">noSuchMethod</a></span><span class="signature">(<wbr><span class="parameter" id="noSuchMethod-param-invocation"><span class="type-annotation"><a href="dart-core/Invocation-class.html">Invocation</a></span> <span class="parameter-name">invocation</span></span>)
            <span class="returntype parameter">&#8594; dynamic</span>
          </span>
                  </dt>
        <dd class="inherited">
          Invoked when a non-existent method or property is accessed. <a href="dart-core/Object/noSuchMethod.html">[...]</a>
                  <div class="features">inherited</div>
</dd>
        <dt id="toString" class="callable inherited">
          <span class="name"><a href="dart-core/Object/toString.html">toString</a></span><span class="signature">(<wbr>)
            <span class="returntype parameter">&#8594; <a href="dart-core/String-class.html">String</a></span>
          </span>
                  </dt>
        <dd class="inherited">
          A string representation of this object. <a href="dart-core/Object/toString.html">[...]</a>
                  <div class="features">inherited</div>
</dd>
      </dl>
    </section>

    <section class="summary offset-anchor inherited" id="operators">
      <h2>Operators</h2>
      <dl class="callables">
        <dt id="operator ==" class="callable inherited">
          <span class="name"><a href="dart-core/Object/operator_equals.html">operator ==</a></span><span class="signature">(<wbr><span class="parameter" id="==-param-other"><span class="type-annotation"><a href="dart-core/Object-class.html">Object</a></span> <span class="parameter-name">other</span></span>)
            <span class="returntype parameter">&#8594; <a href="dart-core/bool-class.html">bool</a></span>
          </span>
                  </dt>
        <dd class="inherited">
          The equality operator. <a href="dart-core/Object/operator_equals.html">[...]</a>
                  <div class="features">inherited</div>
</dd>
      </dl>
    </section>




  </div> <!-- /.main-content -->

  <div id="dartdoc-sidebar-right" class="col-xs-6 col-sm-6 col-md-2 sidebar sidebar-offcanvas-right">
    <ol>
    
      <li class="section-title"><a href="dart-developer/TimelineTask-class.html#constructors">Constructors</a></li>
      <li><a href="dart-developer/TimelineTask/TimelineTask.html">TimelineTask</a></li>
      <li><a href="dart-developer/TimelineTask/TimelineTask.withTaskId.html">withTaskId</a></li>
    
      <li class="section-title inherited">
        <a href="dart-developer/TimelineTask-class.html#instance-properties">Properties</a>
      </li>
      <li class="inherited"><a href="dart-core/Object/hashCode.html">hashCode</a></li>
      <li class="inherited"><a href="dart-core/Object/runtimeType.html">runtimeType</a></li>
    
      <li class="section-title"><a href="dart-developer/TimelineTask-class.html#instance-methods">Methods</a></li>
      <li><a href="dart-developer/TimelineTask/finish.html">finish</a></li>
      <li><a href="dart-developer/TimelineTask/instant.html">instant</a></li>
      <li><a href="dart-developer/TimelineTask/pass.html">pass</a></li>
      <li><a href="dart-developer/TimelineTask/start.html">start</a></li>
      <li class="inherited"><a href="dart-core/Object/noSuchMethod.html">noSuchMethod</a></li>
      <li class="inherited"><a href="dart-core/Object/toString.html">toString</a></li>
    
      <li class="section-title inherited"><a href="dart-developer/TimelineTask-class.html#operators">Operators</a></li>
      <li class="inherited"><a href="dart-core/Object/operator_equals.html">operator ==</a></li>
    
    
    
    
    </ol>
  </div><!--/.sidebar-offcanvas-->

</main>

<footer>
  <span class="no-break">
    gms_check
      1.0.0
  </span>

  <!-- footer-text placeholder -->
</footer>

<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.2.1/jquery.min.js"></script>
<script src="static-assets/typeahead.bundle.min.js"></script>
<script src="static-assets/highlight.pack.js"></script>
<script src="static-assets/URI.js"></script>
<script src="static-assets/script.js"></script>
<!-- footer placeholder -->

</body>

</html>
