<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, height=device-height, initial-scale=1, user-scalable=no">
  <meta name="description" content="dart:developer library API docs, for the Dart programming language.">
  <title>dart:developer library - Dart API</title>
  <!-- required because all the links are pseudo-absolute -->
  <base href="..">

  <link href="https://fonts.googleapis.com/css?family=Source+Code+Pro:500,400i,400,300|Source+Sans+Pro:400,300,700" rel="stylesheet">
  <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
  <link rel="stylesheet" href="static-assets/github.css">
  <link rel="stylesheet" href="static-assets/styles.css">
  <link rel="icon" href="static-assets/favicon.png">
  <!-- header placeholder -->
</head>

<body>

<div id="overlay-under-drawer"></div>

<header id="title">
  <button id="sidenav-left-toggle" type="button">&nbsp;</button>
  <ol class="breadcrumbs gt-separated dark hidden-xs">
    <li><a href="index.html">gms_check</a></li>
    <li class="self-crumb">dart:developer library</li>
  </ol>
  <div class="self-name">dart:developer</div>
  <form class="search navbar-right" role="search">
    <input type="text" id="search-box" autocomplete="off" disabled class="form-control typeahead" placeholder="Loading search...">
  </form>
</header>

<main>

  <div id="dartdoc-sidebar-left" class="col-xs-6 col-sm-3 col-md-2 sidebar sidebar-offcanvas-left">
    <header id="header-search-sidebar" class="hidden-l">
      <form class="search-sidebar" role="search">
        <input type="text" id="search-sidebar" autocomplete="off" disabled class="form-control typeahead" placeholder="Loading search...">
      </form>
    </header>
    
    <ol class="breadcrumbs gt-separated dark hidden-l" id="sidebar-nav">
      <li><a href="index.html">gms_check</a></li>
      <li class="self-crumb">dart:developer library</li>
    </ol>
    
    <h5><span class="package-name">gms_check</span> <span class="package-kind">package</span></h5>
    <ol>
          <li class="section-title">Libraries</li>
          <li><a href="gms_check/gms_check-library.html">gms_check</a></li>
          <li class="section-title">Dart</li>
          <li><a href="dart-ui/dart-ui-library.html">dart:ui</a></li>
          <li class="section-subtitle">Core</li>
            <li class="section-subitem"><a href="dart-async/dart-async-library.html">dart:async</a></li>
            <li class="section-subitem"><a href="dart-collection/dart-collection-library.html">dart:collection</a></li>
            <li class="section-subitem"><a href="dart-convert/dart-convert-library.html">dart:convert</a></li>
            <li class="section-subitem"><a href="dart-core/dart-core-library.html">dart:core</a></li>
            <li class="section-subitem"><a href="dart-developer/dart-developer-library.html">dart:developer</a></li>
            <li class="section-subitem"><a href="dart-math/dart-math-library.html">dart:math</a></li>
            <li class="section-subitem"><a href="dart-typed_data/dart-typed_data-library.html">dart:typed_data</a></li>
          <li class="section-subtitle">VM</li>
            <li class="section-subitem"><a href="dart-ffi/dart-ffi-library.html">dart:ffi</a></li>
            <li class="section-subitem"><a href="dart-io/dart-io-library.html">dart:io</a></li>
            <li class="section-subitem"><a href="dart-isolate/dart-isolate-library.html">dart:isolate</a></li>
          <li class="section-subtitle">Web</li>
            <li class="section-subitem"><a href="dart-html/dart-html-library.html">dart:html</a></li>
            <li class="section-subitem"><a href="dart-js/dart-js-library.html">dart:js</a></li>
            <li class="section-subitem"><a href="dart-js_util/dart-js_util-library.html">dart:js_util</a></li>
    </ol>
  </div>

  <div id="dartdoc-main-content" class="col-xs-12 col-sm-9 col-md-8 main-content">
      <div><h1><span class="kind-library">dart:developer</span> library </h1></div>

    <section class="desc markdown">
      <p>Interact with developer tools such as the debugger and inspector.</p>
<p>This library is platform dependent and has separate implementations for
both web and the Dart VM. A specific platform may not support all
operations.</p>
<p>To use this library in your code:</p>
<pre class="language-dart"><code>import 'dart:developer';
</code></pre>
    </section>
    
    <section class="summary offset-anchor" id="classes">
      <h2>Classes</h2>

      <dl>
        <dt id="Counter">
          <span class="name "><a href="dart-developer/Counter-class.html">Counter</a></span>         
        </dt>
        <dd>
          A changing value. Initial value is 0.0.
        </dd>
        <dt id="Flow">
          <span class="name "><a href="dart-developer/Flow-class.html">Flow</a></span>         
        </dt>
        <dd>
          A class to represent Flow events. <a href="dart-developer/Flow-class.html">[...]</a>
        </dd>
        <dt id="Gauge">
          <span class="name "><a href="dart-developer/Gauge-class.html">Gauge</a></span>         
        </dt>
        <dd>
          A measured value with a min and max. Initial value is min. Value will
be clamped to the interval <code>[min, max]</code>.
        </dd>
        <dt id="Metric">
          <span class="name "><a href="dart-developer/Metric-class.html">Metric</a></span>         
        </dt>
        <dd>
          Abstract <a href="dart-developer/Metric-class.html">Metric</a> class. Metric names must be unique, are hierarchical,
and use periods as separators. For example, 'a.b.c'. Uniqueness is only
enforced when a Metric is registered. The name of a metric cannot contain
the slash ('/') character.
        </dd>
        <dt id="Metrics">
          <span class="name "><a href="dart-developer/Metrics-class.html">Metrics</a></span>         
        </dt>
        <dd>
          
        </dd>
        <dt id="Service">
          <span class="name "><a href="dart-developer/Service-class.html">Service</a></span>         
        </dt>
        <dd>
          Access information about the service protocol and control the web server
that provides access to the services provided by the Dart VM for
debugging and inspecting Dart programs.
        </dd>
        <dt id="ServiceExtensionResponse">
          <span class="name "><a href="dart-developer/ServiceExtensionResponse-class.html">ServiceExtensionResponse</a></span>         
        </dt>
        <dd>
          A response to a service protocol extension RPC. <a href="dart-developer/ServiceExtensionResponse-class.html">[...]</a>
        </dd>
        <dt id="ServiceProtocolInfo">
          <span class="name "><a href="dart-developer/ServiceProtocolInfo-class.html">ServiceProtocolInfo</a></span>         
        </dt>
        <dd>
          Service protocol is the protocol that a client like the Observatory
could use to access the services provided by the Dart VM for
debugging and inspecting Dart programs. This class encapsulates the
version number and Uri for accessing this service.
        </dd>
        <dt id="Timeline">
          <span class="name "><a href="dart-developer/Timeline-class.html">Timeline</a></span>         
        </dt>
        <dd>
          Add to the timeline. <a href="dart-developer/Timeline-class.html">[...]</a>
        </dd>
        <dt id="TimelineTask">
          <span class="name "><a href="dart-developer/TimelineTask-class.html">TimelineTask</a></span>         
        </dt>
        <dd>
          An asynchronous task on the timeline. An asynchronous task can have many
(nested) synchronous operations. Synchronous operations can live longer than
the current isolate event. To pass a <a href="dart-developer/TimelineTask-class.html">TimelineTask</a> to another isolate,
you must first call <a href="dart-developer/TimelineTask/pass.html">pass</a> to get the task id and then construct a new
<a href="dart-developer/TimelineTask-class.html">TimelineTask</a> in the other isolate.
        </dd>
        <dt id="UserTag">
          <span class="name "><a href="dart-developer/UserTag-class.html">UserTag</a></span>         
        </dt>
        <dd>
          A UserTag can be used to group samples in the
<a href="https://flutter.dev/docs/development/tools/devtools/cpu-profiler">DevTools CPU profiler</a>.
        </dd>
      </dl>
    </section>





    <section class="summary offset-anchor" id="functions">
      <h2>Functions</h2>

      <dl class="callables">
        <dt id="debugger" class="callable">
          <span class="name"><a href="dart-developer/debugger.html">debugger</a></span><span class="signature">(<wbr>{<span class="parameter" id="debugger-param-when"><span class="type-annotation"><a href="dart-core/bool-class.html">bool</a></span> <span class="parameter-name">when</span>: <span class="default-value">true</span>, </span> <span class="parameter" id="debugger-param-message"><span class="type-annotation"><a href="dart-core/String-class.html">String</a></span> <span class="parameter-name">message</span></span> })
            <span class="returntype parameter">&#8594; <a href="dart-core/bool-class.html">bool</a></span>
          </span>
                  </dt>
        <dd>
          If <code>when</code> is true, stop the program as if a breakpoint were hit at the
following statement. <a href="dart-developer/debugger.html">[...]</a>
                  
</dd>
        <dt id="getCurrentTag" class="callable">
          <span class="name"><a href="dart-developer/getCurrentTag.html">getCurrentTag</a></span><span class="signature">(<wbr>)
            <span class="returntype parameter">&#8594; <a href="dart-developer/UserTag-class.html">UserTag</a></span>
          </span>
                  </dt>
        <dd>
          Returns the current <a href="dart-developer/UserTag-class.html">UserTag</a> for the isolate.
                  
</dd>
        <dt id="inspect" class="callable">
          <span class="name"><a href="dart-developer/inspect.html">inspect</a></span><span class="signature">(<wbr><span class="parameter" id="inspect-param-object"><span class="type-annotation"><a href="dart-core/Object-class.html">Object</a></span> <span class="parameter-name">object</span></span>)
            <span class="returntype parameter">&#8594; <a href="dart-core/Object-class.html">Object</a></span>
          </span>
                  </dt>
        <dd>
          Send a reference to <code>object</code> to any attached debuggers. <a href="dart-developer/inspect.html">[...]</a>
                  
</dd>
        <dt id="log" class="callable">
          <span class="name"><a href="dart-developer/log.html">log</a></span><span class="signature">(<wbr><span class="parameter" id="log-param-message"><span class="type-annotation"><a href="dart-core/String-class.html">String</a></span> <span class="parameter-name">message</span>, {</span> <span class="parameter" id="log-param-time"><span class="type-annotation"><a href="dart-core/DateTime-class.html">DateTime</a></span> <span class="parameter-name">time</span>, </span> <span class="parameter" id="log-param-sequenceNumber"><span class="type-annotation"><a href="dart-core/int-class.html">int</a></span> <span class="parameter-name">sequenceNumber</span>, </span> <span class="parameter" id="log-param-level"><span class="type-annotation"><a href="dart-core/int-class.html">int</a></span> <span class="parameter-name">level</span>: <span class="default-value">0</span>, </span> <span class="parameter" id="log-param-name"><span class="type-annotation"><a href="dart-core/String-class.html">String</a></span> <span class="parameter-name">name</span>: <span class="default-value">''</span>, </span> <span class="parameter" id="log-param-zone"><span class="type-annotation"><a href="dart-async/Zone-class.html">Zone</a></span> <span class="parameter-name">zone</span>, </span> <span class="parameter" id="log-param-error"><span class="type-annotation"><a href="dart-core/Object-class.html">Object</a></span> <span class="parameter-name">error</span>, </span> <span class="parameter" id="log-param-stackTrace"><span class="type-annotation"><a href="dart-core/StackTrace-class.html">StackTrace</a></span> <span class="parameter-name">stackTrace</span></span> })
            <span class="returntype parameter">&#8594; void</span>
          </span>
                  </dt>
        <dd>
          Emit a log event. <a href="dart-developer/log.html">[...]</a>
                  
</dd>
        <dt id="postEvent" class="callable">
          <span class="name"><a href="dart-developer/postEvent.html">postEvent</a></span><span class="signature">(<wbr><span class="parameter" id="postEvent-param-eventKind"><span class="type-annotation"><a href="dart-core/String-class.html">String</a></span> <span class="parameter-name">eventKind</span>, </span> <span class="parameter" id="postEvent-param-eventData"><span class="type-annotation"><a href="dart-core/Map-class.html">Map</a></span> <span class="parameter-name">eventData</span></span>)
            <span class="returntype parameter">&#8594; void</span>
          </span>
                  </dt>
        <dd>
          Post an event of <code>eventKind</code> with payload of <code>eventData</code> to the <code>Extension</code>
event stream.
                  
</dd>
        <dt id="registerExtension" class="callable">
          <span class="name"><a href="dart-developer/registerExtension.html">registerExtension</a></span><span class="signature">(<wbr><span class="parameter" id="registerExtension-param-method"><span class="type-annotation"><a href="dart-core/String-class.html">String</a></span> <span class="parameter-name">method</span>, </span> <span class="parameter" id="registerExtension-param-handler"><span class="type-annotation"><a href="dart-developer/ServiceExtensionHandler.html">ServiceExtensionHandler</a></span> <span class="parameter-name">handler</span></span>)
            <span class="returntype parameter">&#8594; void</span>
          </span>
                  </dt>
        <dd>
          Register a <a href="dart-developer/ServiceExtensionHandler.html">ServiceExtensionHandler</a> that will be invoked in this isolate
for <code>method</code>. <em>NOTE</em>: Service protocol extensions must be registered
in each isolate. <a href="dart-developer/registerExtension.html">[...]</a>
                  
</dd>
      </dl>
    </section>


    <section class="summary offset-anchor" id="typedefs">
      <h2>Typedefs</h2>

      <dl class="callables">
        <dt id="ServiceExtensionHandler" class="callable">
          <span class="name"><a href="dart-developer/ServiceExtensionHandler.html">ServiceExtensionHandler</a></span><span class="signature">(<wbr><span class="parameter" id="ServiceExtensionHandler-param-method"><span class="type-annotation"><a href="dart-core/String-class.html">String</a></span> <span class="parameter-name">method</span>, </span> <span class="parameter" id="ServiceExtensionHandler-param-parameters"><span class="type-annotation"><a href="dart-core/Map-class.html">Map</a><span class="signature">&lt;<wbr><span class="type-parameter"><a href="dart-core/String-class.html">String</a></span>, <span class="type-parameter"><a href="dart-core/String-class.html">String</a></span>&gt;</span></span> <span class="parameter-name">parameters</span></span>)
            <span class="returntype parameter">&#8594; <a href="dart-async/Future-class.html">Future</a><span class="signature">&lt;<wbr><span class="type-parameter"><a href="dart-developer/ServiceExtensionResponse-class.html">ServiceExtensionResponse</a></span>&gt;</span></span>
          </span>
                  </dt>
        <dd>
          A service protocol extension handler. Registered with <a href="dart-developer/registerExtension.html">registerExtension</a>. <a href="dart-developer/ServiceExtensionHandler.html">[...]</a>
                  
</dd>
        <dt id="TimelineAsyncFunction" class="callable">
          <span class="name"><a href="dart-developer/TimelineAsyncFunction.html">TimelineAsyncFunction</a></span><span class="signature">(<wbr>)
            <span class="returntype parameter">&#8594; <a href="dart-async/Future-class.html">Future</a></span>
          </span>
                  </dt>
        <dd>
          
                  
</dd>
        <dt id="TimelineSyncFunction" class="callable">
          <span class="name"><a href="dart-developer/TimelineSyncFunction.html">TimelineSyncFunction</a></span><span class="signature">&lt;<wbr><span class="type-parameter">T</span>&gt;</span><span class="signature">(<wbr>)
            <span class="returntype parameter">&#8594; T</span>
          </span>
                  </dt>
        <dd>
          A typedef for the function argument to <a href="dart-developer/Timeline/timeSync.html">Timeline.timeSync</a>.
                  
</dd>
      </dl>
    </section>


  </div> <!-- /.main-content -->

  <div id="dartdoc-sidebar-right" class="col-xs-6 col-sm-6 col-md-2 sidebar sidebar-offcanvas-right">
    <h5>dart:developer library</h5>
    <ol>
      <li class="section-title"><a href="dart-developer/dart-developer-library.html#classes">Classes</a></li>
      <li><a href="dart-developer/Counter-class.html">Counter</a></li>
      <li><a href="dart-developer/Flow-class.html">Flow</a></li>
      <li><a href="dart-developer/Gauge-class.html">Gauge</a></li>
      <li><a href="dart-developer/Metric-class.html">Metric</a></li>
      <li><a href="dart-developer/Metrics-class.html">Metrics</a></li>
      <li><a href="dart-developer/Service-class.html">Service</a></li>
      <li><a href="dart-developer/ServiceExtensionResponse-class.html">ServiceExtensionResponse</a></li>
      <li><a href="dart-developer/ServiceProtocolInfo-class.html">ServiceProtocolInfo</a></li>
      <li><a href="dart-developer/Timeline-class.html">Timeline</a></li>
      <li><a href="dart-developer/TimelineTask-class.html">TimelineTask</a></li>
      <li><a href="dart-developer/UserTag-class.html">UserTag</a></li>
    
    
    
    
    
      <li class="section-title"><a href="dart-developer/dart-developer-library.html#functions">Functions</a></li>
      <li><a href="dart-developer/debugger.html">debugger</a></li>
      <li><a href="dart-developer/getCurrentTag.html">getCurrentTag</a></li>
      <li><a href="dart-developer/inspect.html">inspect</a></li>
      <li><a href="dart-developer/log.html">log</a></li>
      <li><a href="dart-developer/postEvent.html">postEvent</a></li>
      <li><a href="dart-developer/registerExtension.html">registerExtension</a></li>
    
    
      <li class="section-title"><a href="dart-developer/dart-developer-library.html#typedefs">Typedefs</a></li>
      <li><a href="dart-developer/ServiceExtensionHandler.html">ServiceExtensionHandler</a></li>
      <li><a href="dart-developer/TimelineAsyncFunction.html">TimelineAsyncFunction</a></li>
      <li><a href="dart-developer/TimelineSyncFunction.html">TimelineSyncFunction</a></li>
    
    </ol>
  </div><!--/sidebar-offcanvas-right-->

</main>

<footer>
  <span class="no-break">
    gms_check
      1.0.0
  </span>

  <!-- footer-text placeholder -->
</footer>

<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.2.1/jquery.min.js"></script>
<script src="static-assets/typeahead.bundle.min.js"></script>
<script src="static-assets/highlight.pack.js"></script>
<script src="static-assets/URI.js"></script>
<script src="static-assets/script.js"></script>
<!-- footer placeholder -->

</body>

</html>
