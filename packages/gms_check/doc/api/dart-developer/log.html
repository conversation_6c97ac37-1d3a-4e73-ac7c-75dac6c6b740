<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, height=device-height, initial-scale=1, user-scalable=no">
  <meta name="description" content="API docs for the log function from the dart:developer library, for the Dart programming language.">
  <title>log function - dart:developer library - Dart API</title>
  <!-- required because all the links are pseudo-absolute -->
  <base href="..">

  <link href="https://fonts.googleapis.com/css?family=Source+Code+Pro:500,400i,400,300|Source+Sans+Pro:400,300,700" rel="stylesheet">
  <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
  <link rel="stylesheet" href="static-assets/github.css">
  <link rel="stylesheet" href="static-assets/styles.css">
  <link rel="icon" href="static-assets/favicon.png">
  <!-- header placeholder -->
</head>

<body>

<div id="overlay-under-drawer"></div>

<header id="title">
  <button id="sidenav-left-toggle" type="button">&nbsp;</button>
  <ol class="breadcrumbs gt-separated dark hidden-xs">
    <li><a href="index.html">gms_check</a></li>
    <li><a href="dart-developer/dart-developer-library.html">dart:developer</a></li>
    <li class="self-crumb">log function</li>
  </ol>
  <div class="self-name">log</div>
  <form class="search navbar-right" role="search">
    <input type="text" id="search-box" autocomplete="off" disabled class="form-control typeahead" placeholder="Loading search...">
  </form>
</header>

<main>

  <div id="dartdoc-sidebar-left" class="col-xs-6 col-sm-3 col-md-2 sidebar sidebar-offcanvas-left">
    <header id="header-search-sidebar" class="hidden-l">
      <form class="search-sidebar" role="search">
        <input type="text" id="search-sidebar" autocomplete="off" disabled class="form-control typeahead" placeholder="Loading search...">
      </form>
    </header>
    
    <ol class="breadcrumbs gt-separated dark hidden-l" id="sidebar-nav">
      <li><a href="index.html">gms_check</a></li>
      <li><a href="dart-developer/dart-developer-library.html">dart:developer</a></li>
      <li class="self-crumb">log function</li>
    </ol>
    
    <h5>dart:developer library</h5>
    <ol>
      <li class="section-title"><a href="dart-developer/dart-developer-library.html#classes">Classes</a></li>
      <li><a href="dart-developer/Counter-class.html">Counter</a></li>
      <li><a href="dart-developer/Flow-class.html">Flow</a></li>
      <li><a href="dart-developer/Gauge-class.html">Gauge</a></li>
      <li><a href="dart-developer/Metric-class.html">Metric</a></li>
      <li><a href="dart-developer/Metrics-class.html">Metrics</a></li>
      <li><a href="dart-developer/Service-class.html">Service</a></li>
      <li><a href="dart-developer/ServiceExtensionResponse-class.html">ServiceExtensionResponse</a></li>
      <li><a href="dart-developer/ServiceProtocolInfo-class.html">ServiceProtocolInfo</a></li>
      <li><a href="dart-developer/Timeline-class.html">Timeline</a></li>
      <li><a href="dart-developer/TimelineTask-class.html">TimelineTask</a></li>
      <li><a href="dart-developer/UserTag-class.html">UserTag</a></li>
    
    
    
    
    
      <li class="section-title"><a href="dart-developer/dart-developer-library.html#functions">Functions</a></li>
      <li><a href="dart-developer/debugger.html">debugger</a></li>
      <li><a href="dart-developer/getCurrentTag.html">getCurrentTag</a></li>
      <li><a href="dart-developer/inspect.html">inspect</a></li>
      <li><a href="dart-developer/log.html">log</a></li>
      <li><a href="dart-developer/postEvent.html">postEvent</a></li>
      <li><a href="dart-developer/registerExtension.html">registerExtension</a></li>
    
    
      <li class="section-title"><a href="dart-developer/dart-developer-library.html#typedefs">Typedefs</a></li>
      <li><a href="dart-developer/ServiceExtensionHandler.html">ServiceExtensionHandler</a></li>
      <li><a href="dart-developer/TimelineAsyncFunction.html">TimelineAsyncFunction</a></li>
      <li><a href="dart-developer/TimelineSyncFunction.html">TimelineSyncFunction</a></li>
    
    </ol>
  </div><!--/.sidebar-offcanvas-left-->

  <div id="dartdoc-main-content" class="col-xs-12 col-sm-9 col-md-8 main-content">
      <div><h1><span class="kind-function">log</span> function </h1></div>

    <section class="multi-line-signature">
        <span class="returntype">void</span>
                <span class="name ">log</span>
(<wbr><span class="parameter" id="log-param-message"><span class="type-annotation"><a href="dart-core/String-class.html">String</a></span> <span class="parameter-name">message</span>, {</span> <span class="parameter" id="log-param-time"><span class="type-annotation"><a href="dart-core/DateTime-class.html">DateTime</a></span> <span class="parameter-name">time</span>, </span> <span class="parameter" id="log-param-sequenceNumber"><span class="type-annotation"><a href="dart-core/int-class.html">int</a></span> <span class="parameter-name">sequenceNumber</span>, </span> <span class="parameter" id="log-param-level"><span class="type-annotation"><a href="dart-core/int-class.html">int</a></span> <span class="parameter-name">level</span>: <span class="default-value">0</span>, </span> <span class="parameter" id="log-param-name"><span class="type-annotation"><a href="dart-core/String-class.html">String</a></span> <span class="parameter-name">name</span>: <span class="default-value">''</span>, </span> <span class="parameter" id="log-param-zone"><span class="type-annotation"><a href="dart-async/Zone-class.html">Zone</a></span> <span class="parameter-name">zone</span>, </span> <span class="parameter" id="log-param-error"><span class="type-annotation"><a href="dart-core/Object-class.html">Object</a></span> <span class="parameter-name">error</span>, </span> <span class="parameter" id="log-param-stackTrace"><span class="type-annotation"><a href="dart-core/StackTrace-class.html">StackTrace</a></span> <span class="parameter-name">stackTrace</span></span> })
    </section>
    <section class="desc markdown">
      <p>Emit a log event.</p>
<p>This function was designed to map closely to the logging information
collected by <code>package:logging</code>.</p>
<ul>
<li><code>message</code> is the log message</li>
<li><code>time</code> (optional) is the timestamp</li>
<li><code>sequenceNumber</code> (optional) is a monotonically increasing sequence number</li>
<li><code>level</code> (optional) is the severity level (a value between 0 and 2000); see
the <code>package:logging</code> <code>Level</code> class for an overview of the possible values</li>
<li><code>name</code> (optional) is the name of the source of the log message</li>
<li><code>zone</code> (optional) the zone where the log was emitted</li>
<li><code>error</code> (optional) an error object associated with this log event</li>
<li><code>stackTrace</code> (optional) a stack trace associated with this log event</li>
</ul>
    </section>
    
    <section class="summary source-code" id="source">
      <h2><span>Implementation</span></h2>
      <pre class="language-dart"><code class="language-dart">external void log(
  String message, {
  DateTime? time,
  int? sequenceNumber,
  int level = 0,
  String name = &#39;&#39;,
  Zone? zone,
  Object? error,
  StackTrace? stackTrace,
});</code></pre>
    </section>

  </div> <!-- /.main-content -->

  <div id="dartdoc-sidebar-right" class="col-xs-6 col-sm-6 col-md-2 sidebar sidebar-offcanvas-right">
  </div><!--/.sidebar-offcanvas-->

</main>

<footer>
  <span class="no-break">
    gms_check
      1.0.0
  </span>

  <!-- footer-text placeholder -->
</footer>

<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.2.1/jquery.min.js"></script>
<script src="static-assets/typeahead.bundle.min.js"></script>
<script src="static-assets/highlight.pack.js"></script>
<script src="static-assets/URI.js"></script>
<script src="static-assets/script.js"></script>
<!-- footer placeholder -->

</body>

</html>
