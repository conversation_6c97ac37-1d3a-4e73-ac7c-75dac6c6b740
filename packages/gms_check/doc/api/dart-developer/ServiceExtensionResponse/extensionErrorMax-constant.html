<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, height=device-height, initial-scale=1, user-scalable=no">
  <meta name="description" content="API docs for the extensionErrorMax constant from the ServiceExtensionResponse class, for the Dart programming language.">
  <title>extensionErrorMax constant - ServiceExtensionResponse class - dart:developer library - Dart API</title>
  <!-- required because all the links are pseudo-absolute -->
  <base href="../..">

  <link href="https://fonts.googleapis.com/css?family=Source+Code+Pro:500,400i,400,300|Source+Sans+Pro:400,300,700" rel="stylesheet">
  <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
  <link rel="stylesheet" href="static-assets/github.css">
  <link rel="stylesheet" href="static-assets/styles.css">
  <link rel="icon" href="static-assets/favicon.png">
  <!-- header placeholder -->
</head>

<body>

<div id="overlay-under-drawer"></div>

<header id="title">
  <button id="sidenav-left-toggle" type="button">&nbsp;</button>
  <ol class="breadcrumbs gt-separated dark hidden-xs">
    <li><a href="index.html">gms_check</a></li>
    <li><a href="dart-developer/dart-developer-library.html">dart:developer</a></li>
    <li><a href="dart-developer/ServiceExtensionResponse-class.html">ServiceExtensionResponse</a></li>
    <li class="self-crumb">extensionErrorMax constant</li>
  </ol>
  <div class="self-name">extensionErrorMax</div>
  <form class="search navbar-right" role="search">
    <input type="text" id="search-box" autocomplete="off" disabled class="form-control typeahead" placeholder="Loading search...">
  </form>
</header>

<main>

  <div id="dartdoc-sidebar-left" class="col-xs-6 col-sm-3 col-md-2 sidebar sidebar-offcanvas-left">
    <header id="header-search-sidebar" class="hidden-l">
      <form class="search-sidebar" role="search">
        <input type="text" id="search-sidebar" autocomplete="off" disabled class="form-control typeahead" placeholder="Loading search...">
      </form>
    </header>
    
    <ol class="breadcrumbs gt-separated dark hidden-l" id="sidebar-nav">
      <li><a href="index.html">gms_check</a></li>
      <li><a href="dart-developer/dart-developer-library.html">dart:developer</a></li>
      <li><a href="dart-developer/ServiceExtensionResponse-class.html">ServiceExtensionResponse</a></li>
      <li class="self-crumb">extensionErrorMax constant</li>
    </ol>
    
    <h5>ServiceExtensionResponse class</h5>
    <ol>
    
        <li class="section-title"><a href="dart-developer/ServiceExtensionResponse-class.html#constructors">Constructors</a></li>
        <li><a href="dart-developer/ServiceExtensionResponse/ServiceExtensionResponse.error.html">error</a></li>
        <li><a href="dart-developer/ServiceExtensionResponse/ServiceExtensionResponse.result.html">result</a></li>
    
        <li class="section-title">
            <a href="dart-developer/ServiceExtensionResponse-class.html#instance-properties">Properties</a>
        </li>
        <li><a href="dart-developer/ServiceExtensionResponse/errorCode.html">errorCode</a></li>
        <li><a href="dart-developer/ServiceExtensionResponse/errorDetail.html">errorDetail</a></li>
        <li><a href="dart-developer/ServiceExtensionResponse/result.html">result</a></li>
        <li class="inherited"><a href="dart-core/Object/hashCode.html">hashCode</a></li>
        <li class="inherited"><a href="dart-core/Object/runtimeType.html">runtimeType</a></li>
    
        <li class="section-title"><a href="dart-developer/ServiceExtensionResponse-class.html#instance-methods">Methods</a></li>
        <li><a href="dart-developer/ServiceExtensionResponse/isError.html">isError</a></li>
        <li class="inherited"><a href="dart-core/Object/noSuchMethod.html">noSuchMethod</a></li>
        <li class="inherited"><a href="dart-core/Object/toString.html">toString</a></li>
    
        <li class="section-title inherited"><a href="dart-developer/ServiceExtensionResponse-class.html#operators">Operators</a></li>
        <li class="inherited"><a href="dart-core/Object/operator_equals.html">operator ==</a></li>
    
    
    
        <li class="section-title"><a href="dart-developer/ServiceExtensionResponse-class.html#constants">Constants</a></li>
        <li><a href="dart-developer/ServiceExtensionResponse/extensionError-constant.html">extensionError</a></li>
        <li><a href="dart-developer/ServiceExtensionResponse/extensionErrorMax-constant.html">extensionErrorMax</a></li>
        <li><a href="dart-developer/ServiceExtensionResponse/extensionErrorMin-constant.html">extensionErrorMin</a></li>
        <li><a href="dart-developer/ServiceExtensionResponse/invalidParams-constant.html">invalidParams</a></li>
        <li><a class="deprecated" href="dart-developer/ServiceExtensionResponse/kExtensionError-constant.html">kExtensionError</a></li>
        <li><a class="deprecated" href="dart-developer/ServiceExtensionResponse/kExtensionErrorMax-constant.html">kExtensionErrorMax</a></li>
        <li><a class="deprecated" href="dart-developer/ServiceExtensionResponse/kExtensionErrorMin-constant.html">kExtensionErrorMin</a></li>
        <li><a class="deprecated" href="dart-developer/ServiceExtensionResponse/kInvalidParams-constant.html">kInvalidParams</a></li>
    
    
    </ol>
  </div><!--/.sidebar-offcanvas-left-->

  <div id="dartdoc-main-content" class="col-xs-12 col-sm-9 col-md-8 main-content">
      <div><h1><span class="kind-constant">extensionErrorMax</span> constant</h1></div>

    <section class="multi-line-signature">
        <span class="returntype"><a href="dart-core/int-class.html">int</a></span>
        const <span class="name ">extensionErrorMax</span>
        =
        <span class="constant-value">-32000</span>
    </section>

    <section class="desc markdown">
      <p>Maximum extension provided error code.</p>
    </section>
        <section class="summary source-code" id="source">
      <h2><span>Implementation</span></h2>
      <pre class="language-dart"><code class="language-dart">static const extensionErrorMax = -32000

</code></pre>
    </section>

  </div> <!-- /.main-content -->

  <div id="dartdoc-sidebar-right" class="col-xs-6 col-sm-6 col-md-2 sidebar sidebar-offcanvas-right">
  </div><!--/.sidebar-offcanvas-->

</main>

<footer>
  <span class="no-break">
    gms_check
      1.0.0
  </span>

  <!-- footer-text placeholder -->
</footer>

<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.2.1/jquery.min.js"></script>
<script src="static-assets/typeahead.bundle.min.js"></script>
<script src="static-assets/highlight.pack.js"></script>
<script src="static-assets/URI.js"></script>
<script src="static-assets/script.js"></script>
<!-- footer placeholder -->

</body>

</html>
