<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, height=device-height, initial-scale=1, user-scalable=no">
  <meta name="description" content="API docs for the controlWebServer method from the Service class, for the Dart programming language.">
  <title>controlWebServer method - Service class - dart:developer library - Dart API</title>
  <!-- required because all the links are pseudo-absolute -->
  <base href="../..">

  <link href="https://fonts.googleapis.com/css?family=Source+Code+Pro:500,400i,400,300|Source+Sans+Pro:400,300,700" rel="stylesheet">
  <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
  <link rel="stylesheet" href="static-assets/github.css">
  <link rel="stylesheet" href="static-assets/styles.css">
  <link rel="icon" href="static-assets/favicon.png">
  <!-- header placeholder -->
</head>

<body>

<div id="overlay-under-drawer"></div>

<header id="title">
  <button id="sidenav-left-toggle" type="button">&nbsp;</button>
  <ol class="breadcrumbs gt-separated dark hidden-xs">
    <li><a href="index.html">gms_check</a></li>
    <li><a href="dart-developer/dart-developer-library.html">dart:developer</a></li>
    <li><a href="dart-developer/Service-class.html">Service</a></li>
    <li class="self-crumb">controlWebServer method</li>
  </ol>
  <div class="self-name">controlWebServer</div>
  <form class="search navbar-right" role="search">
    <input type="text" id="search-box" autocomplete="off" disabled class="form-control typeahead" placeholder="Loading search...">
  </form>
</header>

<main>

  <div id="dartdoc-sidebar-left" class="col-xs-6 col-sm-3 col-md-2 sidebar sidebar-offcanvas-left">
    <header id="header-search-sidebar" class="hidden-l">
      <form class="search-sidebar" role="search">
        <input type="text" id="search-sidebar" autocomplete="off" disabled class="form-control typeahead" placeholder="Loading search...">
      </form>
    </header>
    
    <ol class="breadcrumbs gt-separated dark hidden-l" id="sidebar-nav">
      <li><a href="index.html">gms_check</a></li>
      <li><a href="dart-developer/dart-developer-library.html">dart:developer</a></li>
      <li><a href="dart-developer/Service-class.html">Service</a></li>
      <li class="self-crumb">controlWebServer method</li>
    </ol>
    
    <h5>Service class</h5>
    <ol>
    
        <li class="section-title"><a href="dart-developer/Service-class.html#constructors">Constructors</a></li>
        <li><a href="dart-developer/Service/Service.html">Service</a></li>
    
        <li class="section-title inherited">
            <a href="dart-developer/Service-class.html#instance-properties">Properties</a>
        </li>
        <li class="inherited"><a href="dart-core/Object/hashCode.html">hashCode</a></li>
        <li class="inherited"><a href="dart-core/Object/runtimeType.html">runtimeType</a></li>
    
        <li class="section-title inherited"><a href="dart-developer/Service-class.html#instance-methods">Methods</a></li>
        <li class="inherited"><a href="dart-core/Object/noSuchMethod.html">noSuchMethod</a></li>
        <li class="inherited"><a href="dart-core/Object/toString.html">toString</a></li>
    
        <li class="section-title inherited"><a href="dart-developer/Service-class.html#operators">Operators</a></li>
        <li class="inherited"><a href="dart-core/Object/operator_equals.html">operator ==</a></li>
    
    
        <li class="section-title"><a href="dart-developer/Service-class.html#static-methods">Static methods</a></li>
        <li><a href="dart-developer/Service/controlWebServer.html">controlWebServer</a></li>
        <li><a href="dart-developer/Service/getInfo.html">getInfo</a></li>
        <li><a href="dart-developer/Service/getIsolateID.html">getIsolateID</a></li>
    
    
    
    </ol>
  </div><!--/.sidebar-offcanvas-->

  <div id="dartdoc-main-content" class="col-xs-12 col-sm-9 col-md-8 main-content">
      <div><h1><span class="kind-method">controlWebServer</span> method</h1></div>

    <section class="multi-line-signature">
      <span class="returntype"><a href="dart-async/Future-class.html">Future</a><span class="signature">&lt;<wbr><span class="type-parameter"><a href="dart-developer/ServiceProtocolInfo-class.html">ServiceProtocolInfo</a></span>&gt;</span></span>
            <span class="name ">controlWebServer</span>
(<wbr>{<span class="parameter" id="controlWebServer-param-enable"><span class="type-annotation"><a href="dart-core/bool-class.html">bool</a></span> <span class="parameter-name">enable</span>: <span class="default-value">false</span></span> <span class="parameter" id="controlWebServer-param-silenceOutput"><span class="type-annotation"><a href="dart-core/bool-class.html">bool</a></span> <span class="parameter-name">silenceOutput</span></span> })
      
    </section>
    <section class="desc markdown">
      <p>Control the web server that the service protocol is accessed through.
<code>enable</code> is used as a toggle to enable or disable the web server
servicing requests. If <code>silenceOutput</code> is provided and is true,
the server will not output information to the console.</p>
    </section>
    
    <section class="summary source-code" id="source">
      <h2><span>Implementation</span></h2>
      <pre class="language-dart"><code class="language-dart">static Future&lt;ServiceProtocolInfo&gt; controlWebServer(
    {bool enable = false, bool? silenceOutput}) async {
  &#47;&#47; TODO: When NNBD is complete, delete the following line.
  ArgumentError.checkNotNull(enable, &#39;enable&#39;);
  &#47;&#47; Port to receive response from service isolate.
  final RawReceivePort receivePort =
      new RawReceivePort(null, &#39;Service.controlWebServer&#39;);
  final Completer&lt;String?&gt; completer = new Completer&lt;String?&gt;();
  receivePort.handler = (String? uriString) =&gt; completer.complete(uriString);
  &#47;&#47; Request the information from the service isolate.
  _webServerControl(receivePort.sendPort, enable, silenceOutput);
  &#47;&#47; Await the response from the service isolate.
  String? uriString = await completer.future;
  Uri? uri = uriString == null ? null : Uri.parse(uriString);
  &#47;&#47; Close the port.
  receivePort.close();
  return new ServiceProtocolInfo(uri);
}</code></pre>
    </section>

  </div> <!-- /.main-content -->

  <div id="dartdoc-sidebar-right" class="col-xs-6 col-sm-6 col-md-2 sidebar sidebar-offcanvas-right">
  </div><!--/.sidebar-offcanvas-->

</main>

<footer>
  <span class="no-break">
    gms_check
      1.0.0
  </span>

  <!-- footer-text placeholder -->
</footer>

<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.2.1/jquery.min.js"></script>
<script src="static-assets/typeahead.bundle.min.js"></script>
<script src="static-assets/highlight.pack.js"></script>
<script src="static-assets/URI.js"></script>
<script src="static-assets/script.js"></script>
<!-- footer placeholder -->

</body>

</html>
