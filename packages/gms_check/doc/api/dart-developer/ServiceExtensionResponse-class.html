<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, height=device-height, initial-scale=1, user-scalable=no">
  <meta name="description" content="API docs for the ServiceExtensionResponse class from the dart:developer library, for the Dart programming language.">
  <title>ServiceExtensionResponse class - dart:developer library - Dart API</title>
  <!-- required because all the links are pseudo-absolute -->
  <base href="..">

  <link href="https://fonts.googleapis.com/css?family=Source+Code+Pro:500,400i,400,300|Source+Sans+Pro:400,300,700" rel="stylesheet">
  <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
  <link rel="stylesheet" href="static-assets/github.css">
  <link rel="stylesheet" href="static-assets/styles.css">
  <link rel="icon" href="static-assets/favicon.png">
  <!-- header placeholder -->
</head>

<body>

<div id="overlay-under-drawer"></div>

<header id="title">
  <button id="sidenav-left-toggle" type="button">&nbsp;</button>
  <ol class="breadcrumbs gt-separated dark hidden-xs">
    <li><a href="index.html">gms_check</a></li>
    <li><a href="dart-developer/dart-developer-library.html">dart:developer</a></li>
    <li class="self-crumb">ServiceExtensionResponse class</li>
  </ol>
  <div class="self-name">ServiceExtensionResponse</div>
  <form class="search navbar-right" role="search">
    <input type="text" id="search-box" autocomplete="off" disabled class="form-control typeahead" placeholder="Loading search...">
  </form>
</header>

<main>

  <div id="dartdoc-sidebar-left" class="col-xs-6 col-sm-3 col-md-2 sidebar sidebar-offcanvas-left">
    <header id="header-search-sidebar" class="hidden-l">
      <form class="search-sidebar" role="search">
        <input type="text" id="search-sidebar" autocomplete="off" disabled class="form-control typeahead" placeholder="Loading search...">
      </form>
    </header>
    
    <ol class="breadcrumbs gt-separated dark hidden-l" id="sidebar-nav">
      <li><a href="index.html">gms_check</a></li>
      <li><a href="dart-developer/dart-developer-library.html">dart:developer</a></li>
      <li class="self-crumb">ServiceExtensionResponse class</li>
    </ol>
    
    <h5>dart:developer library</h5>
    <ol>
      <li class="section-title"><a href="dart-developer/dart-developer-library.html#classes">Classes</a></li>
      <li><a href="dart-developer/Counter-class.html">Counter</a></li>
      <li><a href="dart-developer/Flow-class.html">Flow</a></li>
      <li><a href="dart-developer/Gauge-class.html">Gauge</a></li>
      <li><a href="dart-developer/Metric-class.html">Metric</a></li>
      <li><a href="dart-developer/Metrics-class.html">Metrics</a></li>
      <li><a href="dart-developer/Service-class.html">Service</a></li>
      <li><a href="dart-developer/ServiceExtensionResponse-class.html">ServiceExtensionResponse</a></li>
      <li><a href="dart-developer/ServiceProtocolInfo-class.html">ServiceProtocolInfo</a></li>
      <li><a href="dart-developer/Timeline-class.html">Timeline</a></li>
      <li><a href="dart-developer/TimelineTask-class.html">TimelineTask</a></li>
      <li><a href="dart-developer/UserTag-class.html">UserTag</a></li>
    
    
    
    
    
      <li class="section-title"><a href="dart-developer/dart-developer-library.html#functions">Functions</a></li>
      <li><a href="dart-developer/debugger.html">debugger</a></li>
      <li><a href="dart-developer/getCurrentTag.html">getCurrentTag</a></li>
      <li><a href="dart-developer/inspect.html">inspect</a></li>
      <li><a href="dart-developer/log.html">log</a></li>
      <li><a href="dart-developer/postEvent.html">postEvent</a></li>
      <li><a href="dart-developer/registerExtension.html">registerExtension</a></li>
    
    
      <li class="section-title"><a href="dart-developer/dart-developer-library.html#typedefs">Typedefs</a></li>
      <li><a href="dart-developer/ServiceExtensionHandler.html">ServiceExtensionHandler</a></li>
      <li><a href="dart-developer/TimelineAsyncFunction.html">TimelineAsyncFunction</a></li>
      <li><a href="dart-developer/TimelineSyncFunction.html">TimelineSyncFunction</a></li>
    
    </ol>
  </div>

  <div id="dartdoc-main-content" class="col-xs-12 col-sm-9 col-md-8 main-content">
      <div><h1><span class="kind-class">ServiceExtensionResponse</span> class </h1></div>

    <section class="desc markdown">
      <p>A response to a service protocol extension RPC.</p>
<p>If the RPC was successful, use <a href="dart-developer/ServiceExtensionResponse/result.html">ServiceExtensionResponse.result</a>, otherwise
use <a href="dart-developer/ServiceExtensionResponse/ServiceExtensionResponse.error.html">ServiceExtensionResponse.error</a>.</p>
    </section>
    

    <section class="summary offset-anchor" id="constructors">
      <h2>Constructors</h2>

      <dl class="constructor-summary-list">
        <dt id="ServiceExtensionResponse.error" class="callable">
          <span class="name"><a href="dart-developer/ServiceExtensionResponse/ServiceExtensionResponse.error.html">ServiceExtensionResponse.error</a></span><span class="signature">(<span class="parameter" id="error-param-errorCode"><span class="type-annotation"><a href="dart-core/int-class.html">int</a></span> <span class="parameter-name">errorCode</span>, </span> <span class="parameter" id="error-param-errorDetail"><span class="type-annotation"><a href="dart-core/String-class.html">String</a></span> <span class="parameter-name">errorDetail</span></span>)</span>
        </dt>
        <dd>
          Creates an error response to a service protocol extension RPC. <a href="dart-developer/ServiceExtensionResponse/ServiceExtensionResponse.error.html">[...]</a>
        </dd>
        <dt id="ServiceExtensionResponse.result" class="callable">
          <span class="name"><a href="dart-developer/ServiceExtensionResponse/ServiceExtensionResponse.result.html">ServiceExtensionResponse.result</a></span><span class="signature">(<span class="parameter" id="result-param-result"><span class="type-annotation"><a href="dart-core/String-class.html">String</a></span> <span class="parameter-name">result</span></span>)</span>
        </dt>
        <dd>
          Creates a successful response to a service protocol extension RPC. <a href="dart-developer/ServiceExtensionResponse/ServiceExtensionResponse.result.html">[...]</a>
        </dd>
      </dl>
    </section>

    <section class="summary offset-anchor" id="instance-properties">
      <h2>Properties</h2>

      <dl class="properties">
        <dt id="errorCode" class="property">
          <span class="name"><a href="dart-developer/ServiceExtensionResponse/errorCode.html">errorCode</a></span>
          <span class="signature">&#8594; <a href="dart-core/int-class.html">int</a></span>         
        </dt>
        <dd>
          The error code associated with a failed service protocol extension RPC.
                  <div class="features">final</div>
</dd>
        <dt id="errorDetail" class="property">
          <span class="name"><a href="dart-developer/ServiceExtensionResponse/errorDetail.html">errorDetail</a></span>
          <span class="signature">&#8594; <a href="dart-core/String-class.html">String</a></span>         
        </dt>
        <dd>
          The details of a failed service protocol extension RPC.
                  <div class="features">final</div>
</dd>
        <dt id="result" class="property">
          <span class="name"><a href="dart-developer/ServiceExtensionResponse/result.html">result</a></span>
          <span class="signature">&#8594; <a href="dart-core/String-class.html">String</a></span>         
        </dt>
        <dd>
          The result of a successful service protocol extension RPC.
                  <div class="features">final</div>
</dd>
        <dt id="hashCode" class="property inherited">
          <span class="name"><a href="dart-core/Object/hashCode.html">hashCode</a></span>
          <span class="signature">&#8594; <a href="dart-core/int-class.html">int</a></span>         
        </dt>
        <dd class="inherited">
          The hash code for this object. <a href="dart-core/Object/hashCode.html">[...]</a>
                  <div class="features">read-only, inherited</div>
</dd>
        <dt id="runtimeType" class="property inherited">
          <span class="name"><a href="dart-core/Object/runtimeType.html">runtimeType</a></span>
          <span class="signature">&#8594; <a href="dart-core/Type-class.html">Type</a></span>         
        </dt>
        <dd class="inherited">
          A representation of the runtime type of the object.
                  <div class="features">read-only, inherited</div>
</dd>
      </dl>
    </section>

    <section class="summary offset-anchor" id="instance-methods">
      <h2>Methods</h2>
      <dl class="callables">
        <dt id="isError" class="callable">
          <span class="name"><a href="dart-developer/ServiceExtensionResponse/isError.html">isError</a></span><span class="signature">(<wbr>)
            <span class="returntype parameter">&#8594; <a href="dart-core/bool-class.html">bool</a></span>
          </span>
                  </dt>
        <dd>
          Determines if this response represents an error.
                  
</dd>
        <dt id="noSuchMethod" class="callable inherited">
          <span class="name"><a href="dart-core/Object/noSuchMethod.html">noSuchMethod</a></span><span class="signature">(<wbr><span class="parameter" id="noSuchMethod-param-invocation"><span class="type-annotation"><a href="dart-core/Invocation-class.html">Invocation</a></span> <span class="parameter-name">invocation</span></span>)
            <span class="returntype parameter">&#8594; dynamic</span>
          </span>
                  </dt>
        <dd class="inherited">
          Invoked when a non-existent method or property is accessed. <a href="dart-core/Object/noSuchMethod.html">[...]</a>
                  <div class="features">inherited</div>
</dd>
        <dt id="toString" class="callable inherited">
          <span class="name"><a href="dart-core/Object/toString.html">toString</a></span><span class="signature">(<wbr>)
            <span class="returntype parameter">&#8594; <a href="dart-core/String-class.html">String</a></span>
          </span>
                  </dt>
        <dd class="inherited">
          A string representation of this object. <a href="dart-core/Object/toString.html">[...]</a>
                  <div class="features">inherited</div>
</dd>
      </dl>
    </section>

    <section class="summary offset-anchor inherited" id="operators">
      <h2>Operators</h2>
      <dl class="callables">
        <dt id="operator ==" class="callable inherited">
          <span class="name"><a href="dart-core/Object/operator_equals.html">operator ==</a></span><span class="signature">(<wbr><span class="parameter" id="==-param-other"><span class="type-annotation"><a href="dart-core/Object-class.html">Object</a></span> <span class="parameter-name">other</span></span>)
            <span class="returntype parameter">&#8594; <a href="dart-core/bool-class.html">bool</a></span>
          </span>
                  </dt>
        <dd class="inherited">
          The equality operator. <a href="dart-core/Object/operator_equals.html">[...]</a>
                  <div class="features">inherited</div>
</dd>
      </dl>
    </section>



    <section class="summary offset-anchor" id="constants">
      <h2>Constants</h2>

      <dl class="properties">
        <dt id="extensionError" class="constant">
          <span class="name "><a href="dart-developer/ServiceExtensionResponse/extensionError-constant.html">extensionError</a></span>
          <span class="signature">&#8594; const <a href="dart-core/int-class.html">int</a></span>
                  </dt>
        <dd>
          Generic extension error code.
                  
  <div>
            <span class="signature"><code>-32000</code></span>
          </div>
        </dd>
        <dt id="extensionErrorMax" class="constant">
          <span class="name "><a href="dart-developer/ServiceExtensionResponse/extensionErrorMax-constant.html">extensionErrorMax</a></span>
          <span class="signature">&#8594; const <a href="dart-core/int-class.html">int</a></span>
                  </dt>
        <dd>
          Maximum extension provided error code.
                  
  <div>
            <span class="signature"><code>-32000</code></span>
          </div>
        </dd>
        <dt id="extensionErrorMin" class="constant">
          <span class="name "><a href="dart-developer/ServiceExtensionResponse/extensionErrorMin-constant.html">extensionErrorMin</a></span>
          <span class="signature">&#8594; const <a href="dart-core/int-class.html">int</a></span>
                  </dt>
        <dd>
          Minimum extension provided error code.
                  
  <div>
            <span class="signature"><code>-32016</code></span>
          </div>
        </dd>
        <dt id="invalidParams" class="constant">
          <span class="name "><a href="dart-developer/ServiceExtensionResponse/invalidParams-constant.html">invalidParams</a></span>
          <span class="signature">&#8594; const <a href="dart-core/int-class.html">int</a></span>
                  </dt>
        <dd>
          Invalid method parameter(s) error code.
                  
  <div>
            <span class="signature"><code>-32602</code></span>
          </div>
        </dd>
        <dt id="kExtensionError" class="constant">
          <span class="name deprecated"><a class="deprecated" href="dart-developer/ServiceExtensionResponse/kExtensionError-constant.html">kExtensionError</a></span>
          <span class="signature">&#8594; const <a href="dart-core/int-class.html">int</a></span>
                  </dt>
        <dd>
          Generic extension error code.
                  
  <div>
            <span class="signature"><code>extensionError</code></span>
          </div>
        </dd>
        <dt id="kExtensionErrorMax" class="constant">
          <span class="name deprecated"><a class="deprecated" href="dart-developer/ServiceExtensionResponse/kExtensionErrorMax-constant.html">kExtensionErrorMax</a></span>
          <span class="signature">&#8594; const <a href="dart-core/int-class.html">int</a></span>
                  </dt>
        <dd>
          Maximum extension provided error code.
                  
  <div>
            <span class="signature"><code>extensionErrorMax</code></span>
          </div>
        </dd>
        <dt id="kExtensionErrorMin" class="constant">
          <span class="name deprecated"><a class="deprecated" href="dart-developer/ServiceExtensionResponse/kExtensionErrorMin-constant.html">kExtensionErrorMin</a></span>
          <span class="signature">&#8594; const <a href="dart-core/int-class.html">int</a></span>
                  </dt>
        <dd>
          Minimum extension provided error code.
                  
  <div>
            <span class="signature"><code>extensionErrorMin</code></span>
          </div>
        </dd>
        <dt id="kInvalidParams" class="constant">
          <span class="name deprecated"><a class="deprecated" href="dart-developer/ServiceExtensionResponse/kInvalidParams-constant.html">kInvalidParams</a></span>
          <span class="signature">&#8594; const <a href="dart-core/int-class.html">int</a></span>
                  </dt>
        <dd>
          Invalid method parameter(s) error code.
                  
  <div>
            <span class="signature"><code>invalidParams</code></span>
          </div>
        </dd>
      </dl>
    </section>

  </div> <!-- /.main-content -->

  <div id="dartdoc-sidebar-right" class="col-xs-6 col-sm-6 col-md-2 sidebar sidebar-offcanvas-right">
    <ol>
    
      <li class="section-title"><a href="dart-developer/ServiceExtensionResponse-class.html#constructors">Constructors</a></li>
      <li><a href="dart-developer/ServiceExtensionResponse/ServiceExtensionResponse.error.html">error</a></li>
      <li><a href="dart-developer/ServiceExtensionResponse/ServiceExtensionResponse.result.html">result</a></li>
    
      <li class="section-title">
        <a href="dart-developer/ServiceExtensionResponse-class.html#instance-properties">Properties</a>
      </li>
      <li><a href="dart-developer/ServiceExtensionResponse/errorCode.html">errorCode</a></li>
      <li><a href="dart-developer/ServiceExtensionResponse/errorDetail.html">errorDetail</a></li>
      <li><a href="dart-developer/ServiceExtensionResponse/result.html">result</a></li>
      <li class="inherited"><a href="dart-core/Object/hashCode.html">hashCode</a></li>
      <li class="inherited"><a href="dart-core/Object/runtimeType.html">runtimeType</a></li>
    
      <li class="section-title"><a href="dart-developer/ServiceExtensionResponse-class.html#instance-methods">Methods</a></li>
      <li><a href="dart-developer/ServiceExtensionResponse/isError.html">isError</a></li>
      <li class="inherited"><a href="dart-core/Object/noSuchMethod.html">noSuchMethod</a></li>
      <li class="inherited"><a href="dart-core/Object/toString.html">toString</a></li>
    
      <li class="section-title inherited"><a href="dart-developer/ServiceExtensionResponse-class.html#operators">Operators</a></li>
      <li class="inherited"><a href="dart-core/Object/operator_equals.html">operator ==</a></li>
    
    
    
      <li class="section-title"><a href="dart-developer/ServiceExtensionResponse-class.html#constants">Constants</a></li>
      <li><a href="dart-developer/ServiceExtensionResponse/extensionError-constant.html">extensionError</a></li>
      <li><a href="dart-developer/ServiceExtensionResponse/extensionErrorMax-constant.html">extensionErrorMax</a></li>
      <li><a href="dart-developer/ServiceExtensionResponse/extensionErrorMin-constant.html">extensionErrorMin</a></li>
      <li><a href="dart-developer/ServiceExtensionResponse/invalidParams-constant.html">invalidParams</a></li>
      <li><a class="deprecated" href="dart-developer/ServiceExtensionResponse/kExtensionError-constant.html">kExtensionError</a></li>
      <li><a class="deprecated" href="dart-developer/ServiceExtensionResponse/kExtensionErrorMax-constant.html">kExtensionErrorMax</a></li>
      <li><a class="deprecated" href="dart-developer/ServiceExtensionResponse/kExtensionErrorMin-constant.html">kExtensionErrorMin</a></li>
      <li><a class="deprecated" href="dart-developer/ServiceExtensionResponse/kInvalidParams-constant.html">kInvalidParams</a></li>
    
    </ol>
  </div><!--/.sidebar-offcanvas-->

</main>

<footer>
  <span class="no-break">
    gms_check
      1.0.0
  </span>

  <!-- footer-text placeholder -->
</footer>

<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.2.1/jquery.min.js"></script>
<script src="static-assets/typeahead.bundle.min.js"></script>
<script src="static-assets/highlight.pack.js"></script>
<script src="static-assets/URI.js"></script>
<script src="static-assets/script.js"></script>
<!-- footer placeholder -->

</body>

</html>
