<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, height=device-height, initial-scale=1, user-scalable=no">
  <meta name="description" content="API docs for the start method from the TimelineTask class, for the Dart programming language.">
  <title>start method - TimelineTask class - dart:developer library - Dart API</title>
  <!-- required because all the links are pseudo-absolute -->
  <base href="../..">

  <link href="https://fonts.googleapis.com/css?family=Source+Code+Pro:500,400i,400,300|Source+Sans+Pro:400,300,700" rel="stylesheet">
  <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
  <link rel="stylesheet" href="static-assets/github.css">
  <link rel="stylesheet" href="static-assets/styles.css">
  <link rel="icon" href="static-assets/favicon.png">
  <!-- header placeholder -->
</head>

<body>

<div id="overlay-under-drawer"></div>

<header id="title">
  <button id="sidenav-left-toggle" type="button">&nbsp;</button>
  <ol class="breadcrumbs gt-separated dark hidden-xs">
    <li><a href="index.html">gms_check</a></li>
    <li><a href="dart-developer/dart-developer-library.html">dart:developer</a></li>
    <li><a href="dart-developer/TimelineTask-class.html">TimelineTask</a></li>
    <li class="self-crumb">start method</li>
  </ol>
  <div class="self-name">start</div>
  <form class="search navbar-right" role="search">
    <input type="text" id="search-box" autocomplete="off" disabled class="form-control typeahead" placeholder="Loading search...">
  </form>
</header>

<main>

  <div id="dartdoc-sidebar-left" class="col-xs-6 col-sm-3 col-md-2 sidebar sidebar-offcanvas-left">
    <header id="header-search-sidebar" class="hidden-l">
      <form class="search-sidebar" role="search">
        <input type="text" id="search-sidebar" autocomplete="off" disabled class="form-control typeahead" placeholder="Loading search...">
      </form>
    </header>
    
    <ol class="breadcrumbs gt-separated dark hidden-l" id="sidebar-nav">
      <li><a href="index.html">gms_check</a></li>
      <li><a href="dart-developer/dart-developer-library.html">dart:developer</a></li>
      <li><a href="dart-developer/TimelineTask-class.html">TimelineTask</a></li>
      <li class="self-crumb">start method</li>
    </ol>
    
    <h5>TimelineTask class</h5>
    <ol>
    
        <li class="section-title"><a href="dart-developer/TimelineTask-class.html#constructors">Constructors</a></li>
        <li><a href="dart-developer/TimelineTask/TimelineTask.html">TimelineTask</a></li>
        <li><a href="dart-developer/TimelineTask/TimelineTask.withTaskId.html">withTaskId</a></li>
    
        <li class="section-title inherited">
            <a href="dart-developer/TimelineTask-class.html#instance-properties">Properties</a>
        </li>
        <li class="inherited"><a href="dart-core/Object/hashCode.html">hashCode</a></li>
        <li class="inherited"><a href="dart-core/Object/runtimeType.html">runtimeType</a></li>
    
        <li class="section-title"><a href="dart-developer/TimelineTask-class.html#instance-methods">Methods</a></li>
        <li><a href="dart-developer/TimelineTask/finish.html">finish</a></li>
        <li><a href="dart-developer/TimelineTask/instant.html">instant</a></li>
        <li><a href="dart-developer/TimelineTask/pass.html">pass</a></li>
        <li><a href="dart-developer/TimelineTask/start.html">start</a></li>
        <li class="inherited"><a href="dart-core/Object/noSuchMethod.html">noSuchMethod</a></li>
        <li class="inherited"><a href="dart-core/Object/toString.html">toString</a></li>
    
        <li class="section-title inherited"><a href="dart-developer/TimelineTask-class.html#operators">Operators</a></li>
        <li class="inherited"><a href="dart-core/Object/operator_equals.html">operator ==</a></li>
    
    
    
    
    
    </ol>
  </div><!--/.sidebar-offcanvas-->

  <div id="dartdoc-main-content" class="col-xs-12 col-sm-9 col-md-8 main-content">
      <div><h1><span class="kind-method">start</span> method</h1></div>

    <section class="multi-line-signature">
      <span class="returntype">void</span>
            <span class="name ">start</span>
(<wbr><span class="parameter" id="start-param-name"><span class="type-annotation"><a href="dart-core/String-class.html">String</a></span> <span class="parameter-name">name</span>, {</span> <span class="parameter" id="start-param-arguments"><span class="type-annotation"><a href="dart-core/Map-class.html">Map</a></span> <span class="parameter-name">arguments</span></span> })
      
    </section>
    <section class="desc markdown">
      <p>Start a synchronous operation within this task named <code>name</code>.
Optionally takes a <a href="dart-core/Map-class.html">Map</a> of <code>arguments</code>.</p>
    </section>
    
    <section class="summary source-code" id="source">
      <h2><span>Implementation</span></h2>
      <pre class="language-dart"><code class="language-dart">void start(String name, {Map? arguments}) {
  if (!_hasTimeline) return;
  &#47;&#47; TODO: When NNBD is complete, delete the following line.
  ArgumentError.checkNotNull(name, &#39;name&#39;);
  var block = new _AsyncBlock._(name, _taskId);
  _stack.add(block);
  &#47;&#47; TODO(39115): Spurious error about collection literal ambiguity.
  &#47;&#47; TODO(39117): Spurious error about typing of `...?arguments`.
  &#47;&#47; TODO(39120): Spurious error even about `...arguments`.
  &#47;&#47; When these TODOs are done, we can use spread and if elements.
  var map = &lt;Object?, Object?&gt;{};
  if (arguments != null) {
    for (var key in arguments.keys) {
      map[key] = arguments[key];
    }
  }
  if (_parent != null) map[&#39;parentId&#39;] = _parent!._taskId.toRadixString(16);
  if (_filterKey != null) map[_kFilterKey] = _filterKey;
  block._start(map);
}</code></pre>
    </section>

  </div> <!-- /.main-content -->

  <div id="dartdoc-sidebar-right" class="col-xs-6 col-sm-6 col-md-2 sidebar sidebar-offcanvas-right">
  </div><!--/.sidebar-offcanvas-->

</main>

<footer>
  <span class="no-break">
    gms_check
      1.0.0
  </span>

  <!-- footer-text placeholder -->
</footer>

<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.2.1/jquery.min.js"></script>
<script src="static-assets/typeahead.bundle.min.js"></script>
<script src="static-assets/highlight.pack.js"></script>
<script src="static-assets/URI.js"></script>
<script src="static-assets/script.js"></script>
<!-- footer placeholder -->

</body>

</html>
