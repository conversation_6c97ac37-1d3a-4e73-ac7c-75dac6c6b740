<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, height=device-height, initial-scale=1, user-scalable=no">
  <meta name="description" content="API docs for the postEvent function from the dart:developer library, for the Dart programming language.">
  <title>postEvent function - dart:developer library - Dart API</title>
  <!-- required because all the links are pseudo-absolute -->
  <base href="..">

  <link href="https://fonts.googleapis.com/css?family=Source+Code+Pro:500,400i,400,300|Source+Sans+Pro:400,300,700" rel="stylesheet">
  <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
  <link rel="stylesheet" href="static-assets/github.css">
  <link rel="stylesheet" href="static-assets/styles.css">
  <link rel="icon" href="static-assets/favicon.png">
  <!-- header placeholder -->
</head>

<body>

<div id="overlay-under-drawer"></div>

<header id="title">
  <button id="sidenav-left-toggle" type="button">&nbsp;</button>
  <ol class="breadcrumbs gt-separated dark hidden-xs">
    <li><a href="index.html">gms_check</a></li>
    <li><a href="dart-developer/dart-developer-library.html">dart:developer</a></li>
    <li class="self-crumb">postEvent function</li>
  </ol>
  <div class="self-name">postEvent</div>
  <form class="search navbar-right" role="search">
    <input type="text" id="search-box" autocomplete="off" disabled class="form-control typeahead" placeholder="Loading search...">
  </form>
</header>

<main>

  <div id="dartdoc-sidebar-left" class="col-xs-6 col-sm-3 col-md-2 sidebar sidebar-offcanvas-left">
    <header id="header-search-sidebar" class="hidden-l">
      <form class="search-sidebar" role="search">
        <input type="text" id="search-sidebar" autocomplete="off" disabled class="form-control typeahead" placeholder="Loading search...">
      </form>
    </header>
    
    <ol class="breadcrumbs gt-separated dark hidden-l" id="sidebar-nav">
      <li><a href="index.html">gms_check</a></li>
      <li><a href="dart-developer/dart-developer-library.html">dart:developer</a></li>
      <li class="self-crumb">postEvent function</li>
    </ol>
    
    <h5>dart:developer library</h5>
    <ol>
      <li class="section-title"><a href="dart-developer/dart-developer-library.html#classes">Classes</a></li>
      <li><a href="dart-developer/Counter-class.html">Counter</a></li>
      <li><a href="dart-developer/Flow-class.html">Flow</a></li>
      <li><a href="dart-developer/Gauge-class.html">Gauge</a></li>
      <li><a href="dart-developer/Metric-class.html">Metric</a></li>
      <li><a href="dart-developer/Metrics-class.html">Metrics</a></li>
      <li><a href="dart-developer/Service-class.html">Service</a></li>
      <li><a href="dart-developer/ServiceExtensionResponse-class.html">ServiceExtensionResponse</a></li>
      <li><a href="dart-developer/ServiceProtocolInfo-class.html">ServiceProtocolInfo</a></li>
      <li><a href="dart-developer/Timeline-class.html">Timeline</a></li>
      <li><a href="dart-developer/TimelineTask-class.html">TimelineTask</a></li>
      <li><a href="dart-developer/UserTag-class.html">UserTag</a></li>
    
    
    
    
    
      <li class="section-title"><a href="dart-developer/dart-developer-library.html#functions">Functions</a></li>
      <li><a href="dart-developer/debugger.html">debugger</a></li>
      <li><a href="dart-developer/getCurrentTag.html">getCurrentTag</a></li>
      <li><a href="dart-developer/inspect.html">inspect</a></li>
      <li><a href="dart-developer/log.html">log</a></li>
      <li><a href="dart-developer/postEvent.html">postEvent</a></li>
      <li><a href="dart-developer/registerExtension.html">registerExtension</a></li>
    
    
      <li class="section-title"><a href="dart-developer/dart-developer-library.html#typedefs">Typedefs</a></li>
      <li><a href="dart-developer/ServiceExtensionHandler.html">ServiceExtensionHandler</a></li>
      <li><a href="dart-developer/TimelineAsyncFunction.html">TimelineAsyncFunction</a></li>
      <li><a href="dart-developer/TimelineSyncFunction.html">TimelineSyncFunction</a></li>
    
    </ol>
  </div><!--/.sidebar-offcanvas-left-->

  <div id="dartdoc-main-content" class="col-xs-12 col-sm-9 col-md-8 main-content">
      <div><h1><span class="kind-function">postEvent</span> function </h1></div>

    <section class="multi-line-signature">
        <span class="returntype">void</span>
                <span class="name ">postEvent</span>
(<wbr><span class="parameter" id="postEvent-param-eventKind"><span class="type-annotation"><a href="dart-core/String-class.html">String</a></span> <span class="parameter-name">eventKind</span>, </span> <span class="parameter" id="postEvent-param-eventData"><span class="type-annotation"><a href="dart-core/Map-class.html">Map</a></span> <span class="parameter-name">eventData</span></span>)
    </section>
    <section class="desc markdown">
      <p>Post an event of <code>eventKind</code> with payload of <code>eventData</code> to the <code>Extension</code>
event stream.</p>
    </section>
    
    <section class="summary source-code" id="source">
      <h2><span>Implementation</span></h2>
      <pre class="language-dart"><code class="language-dart">void postEvent(String eventKind, Map eventData) {
  &#47;&#47; TODO: When NNBD is complete, delete the following two lines.
  ArgumentError.checkNotNull(eventKind, &#39;eventKind&#39;);
  ArgumentError.checkNotNull(eventData, &#39;eventData&#39;);
  String eventDataAsString = json.encode(eventData);
  _postEvent(eventKind, eventDataAsString);
}</code></pre>
    </section>

  </div> <!-- /.main-content -->

  <div id="dartdoc-sidebar-right" class="col-xs-6 col-sm-6 col-md-2 sidebar sidebar-offcanvas-right">
  </div><!--/.sidebar-offcanvas-->

</main>

<footer>
  <span class="no-break">
    gms_check
      1.0.0
  </span>

  <!-- footer-text placeholder -->
</footer>

<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.2.1/jquery.min.js"></script>
<script src="static-assets/typeahead.bundle.min.js"></script>
<script src="static-assets/highlight.pack.js"></script>
<script src="static-assets/URI.js"></script>
<script src="static-assets/script.js"></script>
<!-- footer placeholder -->

</body>

</html>
