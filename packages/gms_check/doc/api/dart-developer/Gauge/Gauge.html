<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, height=device-height, initial-scale=1, user-scalable=no">
  <meta name="description" content="API docs for the Gauge constructor from the Class Gauge class from the dart:developer library, for the Dart programming language.">
  <title>Gauge constructor - Gauge class - dart:developer library - Dart API</title>
  <!-- required because all the links are pseudo-absolute -->
  <base href="../..">

  <link href="https://fonts.googleapis.com/css?family=Source+Code+Pro:500,400i,400,300|Source+Sans+Pro:400,300,700" rel="stylesheet">
  <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
  <link rel="stylesheet" href="static-assets/github.css">
  <link rel="stylesheet" href="static-assets/styles.css">
  <link rel="icon" href="static-assets/favicon.png">
  <!-- header placeholder -->
</head>

<body>

<div id="overlay-under-drawer"></div>

<header id="title">
  <button id="sidenav-left-toggle" type="button">&nbsp;</button>
  <ol class="breadcrumbs gt-separated dark hidden-xs">
    <li><a href="index.html">gms_check</a></li>
    <li><a href="dart-developer/dart-developer-library.html">dart:developer</a></li>
    <li><a href="dart-developer/Gauge-class.html">Gauge</a></li>
    <li class="self-crumb">Gauge constructor</li>
  </ol>
  <div class="self-name">Gauge</div>
  <form class="search navbar-right" role="search">
    <input type="text" id="search-box" autocomplete="off" disabled class="form-control typeahead" placeholder="Loading search...">
  </form>
</header>

<main>

  <div id="dartdoc-sidebar-left" class="col-xs-6 col-sm-3 col-md-2 sidebar sidebar-offcanvas-left">
    <header id="header-search-sidebar" class="hidden-l">
      <form class="search-sidebar" role="search">
        <input type="text" id="search-sidebar" autocomplete="off" disabled class="form-control typeahead" placeholder="Loading search...">
      </form>
    </header>
    
    <ol class="breadcrumbs gt-separated dark hidden-l" id="sidebar-nav">
      <li><a href="index.html">gms_check</a></li>
      <li><a href="dart-developer/dart-developer-library.html">dart:developer</a></li>
      <li><a href="dart-developer/Gauge-class.html">Gauge</a></li>
      <li class="self-crumb">Gauge constructor</li>
    </ol>
    
    <h5>Gauge class</h5>
    <ol>
    
      <li class="section-title"><a href="dart-developer/Gauge-class.html#constructors">Constructors</a></li>
      <li><a href="dart-developer/Gauge/Gauge.html">Gauge</a></li>
    
      <li class="section-title">
        <a href="dart-developer/Gauge-class.html#instance-properties">Properties</a>
      </li>
      <li><a href="dart-developer/Gauge/max.html">max</a></li>
      <li><a href="dart-developer/Gauge/min.html">min</a></li>
      <li><a href="dart-developer/Gauge/value.html">value</a></li>
      <li class="inherited"><a href="dart-developer/Metric/description.html">description</a></li>
      <li class="inherited"><a href="dart-core/Object/hashCode.html">hashCode</a></li>
      <li class="inherited"><a href="dart-developer/Metric/name.html">name</a></li>
      <li class="inherited"><a href="dart-core/Object/runtimeType.html">runtimeType</a></li>
    
      <li class="section-title"><a href="dart-developer/Gauge-class.html#instance-methods">Methods</a></li>
      <li class="inherited"><a href="dart-core/Object/noSuchMethod.html">noSuchMethod</a></li>
      <li class="inherited"><a href="dart-core/Object/toString.html">toString</a></li>
    
      <li class="section-title inherited"><a href="dart-developer/Gauge-class.html#operators">Operators</a></li>
      <li class="inherited"><a href="dart-core/Object/operator_equals.html">operator ==</a></li>
    
    
    
    
    </ol>
  </div><!--/.sidebar-offcanvas-left-->

  <div id="dartdoc-main-content" class="col-xs-12 col-sm-9 col-md-8 main-content">
      <div><h1><span class="kind-constructor">Gauge</span> constructor</h1></div>

    <section class="multi-line-signature">
      
      <span class="name ">Gauge</span>(<wbr><span class="parameter" id="-param-name"><span class="type-annotation"><a href="dart-core/String-class.html">String</a></span> <span class="parameter-name">name</span>, </span> <span class="parameter" id="-param-description"><span class="type-annotation"><a href="dart-core/String-class.html">String</a></span> <span class="parameter-name">description</span>, </span> <span class="parameter" id="-param-min"><span class="type-annotation"><a href="dart-core/double-class.html">double</a></span> <span class="parameter-name">min</span></span> <span class="parameter" id="-param-max"><span class="type-annotation"><a href="dart-core/double-class.html">double</a></span> <span class="parameter-name">max</span></span>)
    </section>

    
    <section class="summary source-code" id="source">
      <h2><span>Implementation</span></h2>
      <pre class="language-dart"><code class="language-dart">Gauge(String name, String description, this.min, this.max)
    : _value = min,
      super(name, description) {
  &#47;&#47; TODO: When NNBD is complete, delete the following two lines.
  ArgumentError.checkNotNull(min, &#39;min&#39;);
  ArgumentError.checkNotNull(max, &#39;max&#39;);
  if (!(min &lt; max)) throw new ArgumentError(&#39;min must be less than max&#39;);
}</code></pre>
    </section>

  </div> <!-- /.main-content -->

  <div id="dartdoc-sidebar-right" class="col-xs-6 col-sm-6 col-md-2 sidebar sidebar-offcanvas-right">
  </div><!--/.sidebar-offcanvas-->

</main>

<footer>
  <span class="no-break">
    gms_check
      1.0.0
  </span>

  <!-- footer-text placeholder -->
</footer>

<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.2.1/jquery.min.js"></script>
<script src="static-assets/typeahead.bundle.min.js"></script>
<script src="static-assets/highlight.pack.js"></script>
<script src="static-assets/URI.js"></script>
<script src="static-assets/script.js"></script>
<!-- footer placeholder -->

</body>

</html>
